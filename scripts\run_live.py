from pathlib import Path
import argparse
from app.core.wtpy_integration import LiveFacade


def main():
    p = argparse.ArgumentParser(description="Run wtpy live trading engine")
    p.add_argument("--config", default=str(Path("app/config/engine.config.json").resolve()))
    args = p.parse_args()

    fac = LiveFacade()
    res = fac.run(config_path=args.config)
    print({"ok": res.ok, "message": res.message})


if __name__ == "__main__":
    main()

