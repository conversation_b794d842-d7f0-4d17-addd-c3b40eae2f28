"""
监控服务API路由

提供系统监控相关的REST API接口，包括：
- 系统状态监控
- 策略运行监控
- 风险监控
- 性能监控
- WebSocket实时数据推送
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, HTTPException, Depends, WebSocket, WebSocketDisconnect
from pydantic import BaseModel, Field
import json

from ...core.engine import WtpyEngine
from ...exec.risk_engine import RiskEngine
from ...exec.portfolio import PortfolioManager

logger = logging.getLogger(__name__)

router = APIRouter()

# WebSocket连接管理
class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        
    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        
    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
        
    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                # 连接已断开，移除
                self.active_connections.remove(connection)

manager = ConnectionManager()


# Pydantic模型定义
class SystemStatus(BaseModel):
    """系统状态"""
    status: str
    uptime: float
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    timestamp: str


class StrategyMonitor(BaseModel):
    """策略监控信息"""
    strategy_id: str
    name: str
    status: str
    pnl: float
    positions: int
    orders: int
    last_signal_time: Optional[str]
    error_count: int


class RiskMetrics(BaseModel):
    """风险指标"""
    total_exposure: float
    max_drawdown: float
    var_95: float
    portfolio_beta: float
    concentration_risk: float
    leverage_ratio: float
    timestamp: str


# 依赖注入
def get_engine() -> WtpyEngine:
    """获取引擎"""
    from ...api.main import app_state
    if not app_state['initialized'] or not app_state['engine']:
        raise HTTPException(status_code=503, detail="Engine not available")
    return app_state['engine']


@router.get("/system/status", response_model=SystemStatus)
async def get_system_status():
    """
    获取系统状态
    """
    try:
        import psutil
        import time
        
        # 获取系统资源使用情况
        cpu_usage = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        network = psutil.net_io_counters()
        
        # 计算运行时间（简化处理）
        uptime = time.time() - psutil.boot_time()
        
        return SystemStatus(
            status="running",
            uptime=uptime,
            cpu_usage=cpu_usage,
            memory_usage=memory.percent,
            disk_usage=disk.percent,
            network_io={
                "bytes_sent": float(network.bytes_sent),
                "bytes_recv": float(network.bytes_recv)
            },
            timestamp=datetime.now().isoformat()
        )
        
    except ImportError:
        # 如果没有psutil，返回模拟数据
        return SystemStatus(
            status="running",
            uptime=3600.0,
            cpu_usage=25.5,
            memory_usage=45.2,
            disk_usage=60.8,
            network_io={"bytes_sent": 1024000.0, "bytes_recv": 2048000.0},
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        logger.error(f"Error getting system status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/strategies/", response_model=List[StrategyMonitor])
async def get_strategy_monitors(
    engine: WtpyEngine = Depends(get_engine)
):
    """
    获取策略监控信息
    """
    try:
        strategies = engine.strategy_manager.list_strategies()
        
        monitors = []
        for strategy_id, strategy_info in strategies.items():
            # 获取策略实例
            strategy = engine.strategy_manager.get_strategy(strategy_id)
            
            # 计算监控指标
            pnl = 0.0
            positions = 0
            orders = 0
            error_count = 0
            last_signal_time = None
            
            if strategy:
                stats = strategy.get_stats()
                pnl = stats.get('total_pnl', 0.0)
                positions = len([p for p in strategy.get_positions().values() if abs(p) > 0])
                orders = stats.get('orders_placed', 0)
                error_count = stats.get('error_count', 0)
                
                # 获取最后信号时间
                if hasattr(strategy, '_last_signal_time'):
                    last_signal_time = strategy._last_signal_time.isoformat()
            
            monitor = StrategyMonitor(
                strategy_id=strategy_id,
                name=strategy_info.name,
                status=strategy_info.status.value,
                pnl=pnl,
                positions=positions,
                orders=orders,
                last_signal_time=last_signal_time,
                error_count=error_count
            )
            monitors.append(monitor)
            
        return monitors
        
    except Exception as e:
        logger.error(f"Error getting strategy monitors: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/strategies/{strategy_id}/performance")
async def get_strategy_performance(
    strategy_id: str,
    period: str = "1d",
    engine: WtpyEngine = Depends(get_engine)
):
    """
    获取策略性能数据
    
    Args:
        period: 时间周期 (1h, 1d, 1w, 1m)
    """
    try:
        strategy = engine.strategy_manager.get_strategy(strategy_id)
        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        # 获取策略统计信息
        stats = strategy.get_stats()
        
        # 生成时间序列数据（简化处理）
        now = datetime.now()
        if period == "1h":
            time_points = [now - timedelta(minutes=i*5) for i in range(12)]
        elif period == "1d":
            time_points = [now - timedelta(hours=i) for i in range(24)]
        elif period == "1w":
            time_points = [now - timedelta(days=i) for i in range(7)]
        else:  # 1m
            time_points = [now - timedelta(days=i) for i in range(30)]
            
        # 模拟性能数据
        performance_data = []
        base_pnl = stats.get('total_pnl', 0.0)
        
        for i, time_point in enumerate(reversed(time_points)):
            pnl = base_pnl * (i + 1) / len(time_points)
            performance_data.append({
                "timestamp": time_point.isoformat(),
                "pnl": round(pnl, 2),
                "positions": stats.get('positions_count', 0),
                "trades": stats.get('total_trades', 0)
            })
            
        return {
            "strategy_id": strategy_id,
            "period": period,
            "data": performance_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy performance {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk/metrics", response_model=RiskMetrics)
async def get_risk_metrics():
    """
    获取风险指标
    """
    try:
        # 这里应该从风险引擎获取实际数据
        # 简化处理，返回模拟数据
        return RiskMetrics(
            total_exposure=1500000.0,
            max_drawdown=0.08,
            var_95=25000.0,
            portfolio_beta=1.15,
            concentration_risk=0.25,
            leverage_ratio=1.8,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error getting risk metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/risk/alerts")
async def get_risk_alerts(
    severity: Optional[str] = None,
    limit: int = 50
):
    """
    获取风险警报
    
    Args:
        severity: 严重程度过滤 (low, medium, high, critical)
        limit: 返回数量限制
    """
    try:
        # 模拟风险警报数据
        alerts = [
            {
                "id": "alert_001",
                "type": "position_limit",
                "severity": "high",
                "message": "策略A持仓超过限制",
                "strategy_id": "strategy_001",
                "timestamp": (datetime.now() - timedelta(minutes=5)).isoformat(),
                "status": "active"
            },
            {
                "id": "alert_002", 
                "type": "drawdown",
                "severity": "medium",
                "message": "组合回撤达到警戒线",
                "strategy_id": None,
                "timestamp": (datetime.now() - timedelta(minutes=15)).isoformat(),
                "status": "acknowledged"
            }
        ]
        
        # 应用过滤条件
        if severity:
            alerts = [alert for alert in alerts if alert['severity'] == severity]
            
        return {"alerts": alerts[:limit]}
        
    except Exception as e:
        logger.error(f"Error getting risk alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/portfolio/summary")
async def get_portfolio_summary():
    """
    获取投资组合摘要
    """
    try:
        # 这里应该从投资组合管理器获取实际数据
        # 简化处理，返回模拟数据
        return {
            "total_value": 2500000.0,
            "cash": 150000.0,
            "positions_value": 2350000.0,
            "total_pnl": 125000.0,
            "daily_pnl": 8500.0,
            "position_count": 15,
            "top_positions": [
                {"symbol": "000001.SZ", "value": 250000.0, "weight": 0.106},
                {"symbol": "600036.SH", "value": 200000.0, "weight": 0.085},
                {"symbol": "000858.SZ", "value": 180000.0, "weight": 0.077}
            ],
            "sector_allocation": {
                "金融": 0.35,
                "消费": 0.25,
                "科技": 0.20,
                "医药": 0.15,
                "其他": 0.05
            },
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting portfolio summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/logs/recent")
async def get_recent_logs(
    level: str = "INFO",
    limit: int = 100,
    strategy_id: Optional[str] = None
):
    """
    获取最近的日志记录
    
    Args:
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        limit: 返回数量限制
        strategy_id: 策略ID过滤
    """
    try:
        # 这里应该从日志系统获取实际日志
        # 简化处理，返回模拟日志
        logs = []
        
        for i in range(min(limit, 20)):
            log_entry = {
                "timestamp": (datetime.now() - timedelta(minutes=i)).isoformat(),
                "level": level,
                "logger": f"wtpy_quant.strategy.{strategy_id}" if strategy_id else "wtpy_quant.core",
                "message": f"模拟日志消息 {i+1}",
                "strategy_id": strategy_id
            }
            logs.append(log_entry)
            
        return {"logs": logs}
        
    except Exception as e:
        logger.error(f"Error getting recent logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# WebSocket端点
@router.websocket("/ws/realtime")
async def websocket_endpoint(websocket: WebSocket):
    """
    实时数据WebSocket端点
    """
    await manager.connect(websocket)
    try:
        while True:
            # 等待客户端消息
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # 处理订阅请求
            if message.get("type") == "subscribe":
                channels = message.get("channels", [])
                await websocket.send_text(json.dumps({
                    "type": "subscription_confirmed",
                    "channels": channels
                }))
                
                # 开始推送数据
                asyncio.create_task(push_realtime_data(websocket, channels))
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)


async def push_realtime_data(websocket: WebSocket, channels: List[str]):
    """
    推送实时数据
    """
    try:
        while websocket in manager.active_connections:
            for channel in channels:
                if channel == "system_status":
                    # 推送系统状态
                    status_data = {
                        "type": "system_status",
                        "data": {
                            "cpu_usage": 25.5,
                            "memory_usage": 45.2,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await websocket.send_text(json.dumps(status_data))
                    
                elif channel == "strategy_pnl":
                    # 推送策略盈亏
                    pnl_data = {
                        "type": "strategy_pnl",
                        "data": {
                            "strategy_001": 1250.0,
                            "strategy_002": -350.0,
                            "timestamp": datetime.now().isoformat()
                        }
                    }
                    await websocket.send_text(json.dumps(pnl_data))
                    
            await asyncio.sleep(5)  # 5秒推送一次
            
    except Exception as e:
        logger.error(f"Error pushing realtime data: {e}")


@router.get("/health/components")
async def get_component_health():
    """
    获取各组件健康状态
    """
    try:
        from ...api.main import app_state
        
        components = {
            "engine": {
                "status": "healthy" if app_state.get('engine') else "unhealthy",
                "last_check": datetime.now().isoformat()
            },
            "data_feed": {
                "status": "healthy",
                "last_update": datetime.now().isoformat(),
                "symbols_count": 4000
            },
            "risk_engine": {
                "status": "healthy",
                "alerts_count": 2,
                "last_check": datetime.now().isoformat()
            },
            "database": {
                "status": "healthy",
                "connection_pool": "8/10",
                "last_query": datetime.now().isoformat()
            }
        }
        
        return {"components": components}
        
    except Exception as e:
        logger.error(f"Error getting component health: {e}")
        raise HTTPException(status_code=500, detail=str(e))
