"""
长期存储接口 - 历史数据存储与查询

提供历史数据的长期存储和查询功能，包括：
- Parquet文件存储
- 数据库存储接口（ClickHouse/PostgreSQL）
- 批量数据导入导出
- 数据压缩与归档
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, date
import pandas as pd
import numpy as np

from .market_adapter import TickData, BarData

logger = logging.getLogger(__name__)


class LongTermStoreInterface(ABC):
    """长期存储接口"""
    
    @abstractmethod
    async def put_ticks(self, symbol: str, ticks: List[TickData], date_str: Optional[str] = None) -> bool:
        """存储Tick数据"""
        pass
        
    @abstractmethod
    async def put_bars(self, symbol: str, bars: List[BarData], period: str = "1m", date_str: Optional[str] = None) -> bool:
        """存储Bar数据"""
        pass
        
    @abstractmethod
    async def query_ticks(self, symbol: str, start_date: str, end_date: str) -> List[TickData]:
        """查询Tick数据"""
        pass
        
    @abstractmethod
    async def query_bars(self, symbol: str, period: str, start_date: str, end_date: str) -> List[BarData]:
        """查询Bar数据"""
        pass
        
    @abstractmethod
    async def compact(self, symbol: str, date_str: str) -> bool:
        """数据压缩"""
        pass
        
    @abstractmethod
    async def backfill(self, symbol: str, start_date: str, end_date: str) -> bool:
        """数据回填"""
        pass


class ParquetStore(LongTermStoreInterface):
    """Parquet文件存储"""
    
    def __init__(self, base_path: str = "data/parquet"):
        """
        初始化Parquet存储
        
        Args:
            base_path: 基础存储路径
        """
        self.base_path = Path(base_path)
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    def _get_tick_file_path(self, symbol: str, date_str: str) -> Path:
        """获取Tick文件路径"""
        return self.base_path / "ticks" / symbol / f"{date_str}.parquet"
        
    def _get_bar_file_path(self, symbol: str, period: str, date_str: str) -> Path:
        """获取Bar文件路径"""
        return self.base_path / "bars" / period / symbol / f"{date_str}.parquet"
        
    def _tick_to_dataframe(self, ticks: List[TickData]) -> pd.DataFrame:
        """将Tick数据转换为DataFrame"""
        data = []
        for tick in ticks:
            data.append({
                'datetime': tick.datetime,
                'price': tick.price,
                'volume': tick.volume,
                'turnover': tick.turnover,
                'bid_price': tick.bid_price,
                'ask_price': tick.ask_price,
                'bid_volume': tick.bid_volume,
                'ask_volume': tick.ask_volume,
                'open_interest': tick.open_interest
            })
        return pd.DataFrame(data)
        
    def _bar_to_dataframe(self, bars: List[BarData]) -> pd.DataFrame:
        """将Bar数据转换为DataFrame"""
        data = []
        for bar in bars:
            data.append({
                'datetime': bar.datetime,
                'open': bar.open,
                'high': bar.high,
                'low': bar.low,
                'close': bar.close,
                'volume': bar.volume,
                'turnover': bar.turnover
            })
        return pd.DataFrame(data)
        
    def _dataframe_to_ticks(self, df: pd.DataFrame, symbol: str) -> List[TickData]:
        """将DataFrame转换为Tick数据"""
        ticks = []
        for _, row in df.iterrows():
            tick = TickData(
                symbol=symbol,
                datetime=row['datetime'],
                price=row['price'],
                volume=row['volume'],
                turnover=row['turnover'],
                bid_price=row['bid_price'],
                ask_price=row['ask_price'],
                bid_volume=row['bid_volume'],
                ask_volume=row['ask_volume'],
                open_interest=row['open_interest']
            )
            ticks.append(tick)
        return ticks
        
    def _dataframe_to_bars(self, df: pd.DataFrame, symbol: str, period: str) -> List[BarData]:
        """将DataFrame转换为Bar数据"""
        bars = []
        for _, row in df.iterrows():
            bar = BarData(
                symbol=symbol,
                datetime=row['datetime'],
                period=period,
                open=row['open'],
                high=row['high'],
                low=row['low'],
                close=row['close'],
                volume=row['volume'],
                turnover=row['turnover']
            )
            bars.append(bar)
        return bars
        
    async def put_ticks(self, symbol: str, ticks: List[TickData], date_str: Optional[str] = None) -> bool:
        """存储Tick数据"""
        try:
            if not ticks:
                return True
                
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")
                
            file_path = self._get_tick_file_path(symbol, date_str)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为DataFrame
            df = self._tick_to_dataframe(ticks)
            
            # 如果文件已存在，追加数据
            if file_path.exists():
                existing_df = pd.read_parquet(file_path)
                df = pd.concat([existing_df, df], ignore_index=True)
                # 去重并排序
                df = df.drop_duplicates(subset=['datetime']).sort_values('datetime')
            
            # 保存到Parquet文件
            df.to_parquet(file_path, compression='snappy', index=False)
            
            logger.debug(f"Stored {len(ticks)} ticks for {symbol} on {date_str}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing ticks for {symbol}: {e}")
            return False
            
    async def put_bars(self, symbol: str, bars: List[BarData], period: str = "1m", date_str: Optional[str] = None) -> bool:
        """存储Bar数据"""
        try:
            if not bars:
                return True
                
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")
                
            file_path = self._get_bar_file_path(symbol, period, date_str)
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 转换为DataFrame
            df = self._bar_to_dataframe(bars)
            
            # 如果文件已存在，追加数据
            if file_path.exists():
                existing_df = pd.read_parquet(file_path)
                df = pd.concat([existing_df, df], ignore_index=True)
                # 去重并排序
                df = df.drop_duplicates(subset=['datetime']).sort_values('datetime')
            
            # 保存到Parquet文件
            df.to_parquet(file_path, compression='snappy', index=False)
            
            logger.debug(f"Stored {len(bars)} bars for {symbol} {period} on {date_str}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing bars for {symbol}: {e}")
            return False
            
    async def query_ticks(self, symbol: str, start_date: str, end_date: str) -> List[TickData]:
        """查询Tick数据"""
        try:
            ticks = []
            
            # 生成日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            current_dt = start_dt
            while current_dt <= end_dt:
                date_str = current_dt.strftime("%Y%m%d")
                file_path = self._get_tick_file_path(symbol, date_str)
                
                if file_path.exists():
                    df = pd.read_parquet(file_path)
                    day_ticks = self._dataframe_to_ticks(df, symbol)
                    ticks.extend(day_ticks)
                
                current_dt = current_dt.replace(day=current_dt.day + 1)
            
            logger.debug(f"Queried {len(ticks)} ticks for {symbol} from {start_date} to {end_date}")
            return ticks
            
        except Exception as e:
            logger.error(f"Error querying ticks for {symbol}: {e}")
            return []
            
    async def query_bars(self, symbol: str, period: str, start_date: str, end_date: str) -> List[BarData]:
        """查询Bar数据"""
        try:
            bars = []
            
            # 生成日期范围
            start_dt = datetime.strptime(start_date, "%Y%m%d")
            end_dt = datetime.strptime(end_date, "%Y%m%d")
            
            current_dt = start_dt
            while current_dt <= end_dt:
                date_str = current_dt.strftime("%Y%m%d")
                file_path = self._get_bar_file_path(symbol, period, date_str)
                
                if file_path.exists():
                    df = pd.read_parquet(file_path)
                    day_bars = self._dataframe_to_bars(df, symbol, period)
                    bars.extend(day_bars)
                
                current_dt = current_dt.replace(day=current_dt.day + 1)
            
            logger.debug(f"Queried {len(bars)} bars for {symbol} {period} from {start_date} to {end_date}")
            return bars
            
        except Exception as e:
            logger.error(f"Error querying bars for {symbol}: {e}")
            return []
            
    async def compact(self, symbol: str, date_str: str) -> bool:
        """数据压缩（Parquet已经是压缩格式）"""
        try:
            # 对于Parquet，可以重新压缩或合并小文件
            logger.debug(f"Compact operation for {symbol} on {date_str} (no-op for Parquet)")
            return True
        except Exception as e:
            logger.error(f"Error compacting data for {symbol}: {e}")
            return False
            
    async def backfill(self, symbol: str, start_date: str, end_date: str) -> bool:
        """数据回填"""
        try:
            # 实现数据回填逻辑
            logger.info(f"Backfill data for {symbol} from {start_date} to {end_date}")
            return True
        except Exception as e:
            logger.error(f"Error backfilling data for {symbol}: {e}")
            return False
            
    def get_available_dates(self, symbol: str, data_type: str = "ticks") -> List[str]:
        """获取可用的数据日期"""
        try:
            dates = []
            if data_type == "ticks":
                symbol_dir = self.base_path / "ticks" / symbol
            else:
                symbol_dir = self.base_path / "bars" / "1m" / symbol  # 默认1m
                
            if symbol_dir.exists():
                for file_path in symbol_dir.glob("*.parquet"):
                    date_str = file_path.stem
                    dates.append(date_str)
                    
            return sorted(dates)
            
        except Exception as e:
            logger.error(f"Error getting available dates for {symbol}: {e}")
            return []


class LongTermStore:
    """长期存储管理器"""
    
    def __init__(self, store_type: str = "parquet", config: Optional[Dict[str, Any]] = None):
        """
        初始化长期存储
        
        Args:
            store_type: 存储类型 (parquet, clickhouse, postgresql)
            config: 配置参数
        """
        self.store_type = store_type
        self.config = config or {}
        
        # 创建存储实例
        if store_type == "parquet":
            base_path = self.config.get("path", "data/parquet")
            self._store = ParquetStore(base_path)
        elif store_type == "clickhouse":
            # TODO: 实现ClickHouse存储
            raise NotImplementedError("ClickHouse store not implemented yet")
        elif store_type == "postgresql":
            # TODO: 实现PostgreSQL存储
            raise NotImplementedError("PostgreSQL store not implemented yet")
        else:
            raise ValueError(f"Unsupported store type: {store_type}")
            
    async def put_ticks(self, symbol: str, ticks: List[TickData], date_str: Optional[str] = None) -> bool:
        """存储Tick数据"""
        return await self._store.put_ticks(symbol, ticks, date_str)
        
    async def put_bars(self, symbol: str, bars: List[BarData], period: str = "1m", date_str: Optional[str] = None) -> bool:
        """存储Bar数据"""
        return await self._store.put_bars(symbol, bars, period, date_str)
        
    async def query_ticks(self, symbol: str, start_date: str, end_date: str) -> List[TickData]:
        """查询Tick数据"""
        return await self._store.query_ticks(symbol, start_date, end_date)
        
    async def query_bars(self, symbol: str, period: str, start_date: str, end_date: str) -> List[BarData]:
        """查询Bar数据"""
        return await self._store.query_bars(symbol, period, start_date, end_date)
        
    async def compact(self, symbol: str, date_str: str) -> bool:
        """数据压缩"""
        return await self._store.compact(symbol, date_str)
        
    async def backfill(self, symbol: str, start_date: str, end_date: str) -> bool:
        """数据回填"""
        return await self._store.backfill(symbol, start_date, end_date)
        
    def get_store_type(self) -> str:
        """获取存储类型"""
        return self.store_type
        
    def get_store_instance(self) -> LongTermStoreInterface:
        """获取存储实例"""
        return self._store
