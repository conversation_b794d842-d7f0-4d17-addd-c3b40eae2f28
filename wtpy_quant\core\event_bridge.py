"""
事件桥接模块 - 事件分发与处理

负责在wtpy引擎和系统各模块之间桥接事件，包括：
- 行情数据事件
- 交易事件
- 策略事件
- 系统事件
"""

import asyncio
import logging
from typing import Any, Callable, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum
import time
from collections import defaultdict

logger = logging.getLogger(__name__)


class EventType(Enum):
    """事件类型枚举"""
    # 行情事件
    TICK = "tick"
    BAR = "bar"
    
    # 交易事件
    ORDER = "order"
    TRADE = "trade"
    POSITION = "position"
    ACCOUNT = "account"
    
    # 策略事件
    STRATEGY_INIT = "strategy_init"
    STRATEGY_START = "strategy_start"
    STRATEGY_STOP = "strategy_stop"
    STRATEGY_ERROR = "strategy_error"
    
    # 系统事件
    ENGINE_START = "engine_start"
    ENGINE_STOP = "engine_stop"
    ENGINE_ERROR = "engine_error"
    
    # 风控事件
    RISK_WARNING = "risk_warning"
    RISK_BREACH = "risk_breach"


@dataclass
class Event:
    """事件数据结构"""
    type: EventType
    data: Any
    timestamp: float
    source: str = ""
    symbol: str = ""
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


class EventHandler:
    """事件处理器基类"""
    
    def __init__(self, name: str):
        self.name = name
        
    async def handle(self, event: Event) -> None:
        """处理事件"""
        raise NotImplementedError
        
    def can_handle(self, event_type: EventType) -> bool:
        """判断是否可以处理指定类型的事件"""
        return True


class EventBridge:
    """事件桥接器"""
    
    def __init__(self):
        self._handlers: Dict[EventType, List[EventHandler]] = defaultdict(list)
        self._async_handlers: Dict[EventType, List[Callable]] = defaultdict(list)
        self._event_queue: asyncio.Queue = asyncio.Queue()
        self._running = False
        self._worker_task: Optional[asyncio.Task] = None
        self._stats = {
            'events_processed': 0,
            'events_failed': 0,
            'handlers_count': 0
        }
        
    def register_handler(self, event_type: EventType, handler: EventHandler) -> None:
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理器
        """
        self._handlers[event_type].append(handler)
        self._stats['handlers_count'] += 1
        logger.info(f"Registered handler {handler.name} for event type {event_type.value}")
        
    def register_async_handler(self, event_type: EventType, handler: Callable) -> None:
        """
        注册异步事件处理器
        
        Args:
            event_type: 事件类型
            handler: 异步处理函数
        """
        self._async_handlers[event_type].append(handler)
        self._stats['handlers_count'] += 1
        logger.info(f"Registered async handler for event type {event_type.value}")
        
    def unregister_handler(self, event_type: EventType, handler: EventHandler) -> None:
        """
        注销事件处理器
        
        Args:
            event_type: 事件类型
            handler: 事件处理器
        """
        if handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            self._stats['handlers_count'] -= 1
            logger.info(f"Unregistered handler {handler.name} for event type {event_type.value}")
            
    async def emit(self, event: Event) -> None:
        """
        发送事件
        
        Args:
            event: 事件对象
        """
        if not self._running:
            logger.warning("Event bridge is not running, event will be queued")
            
        await self._event_queue.put(event)
        
    def emit_sync(self, event_type: EventType, data: Any, source: str = "", symbol: str = "") -> None:
        """
        同步发送事件（用于wtpy回调）
        
        Args:
            event_type: 事件类型
            data: 事件数据
            source: 事件源
            symbol: 标的代码
        """
        event = Event(
            type=event_type,
            data=data,
            timestamp=time.time(),
            source=source,
            symbol=symbol
        )
        
        # 创建异步任务
        try:
            loop = asyncio.get_event_loop()
            loop.create_task(self.emit(event))
        except RuntimeError:
            # 如果没有运行的事件循环，直接处理
            asyncio.create_task(self.emit(event))
            
    async def start(self) -> None:
        """启动事件桥接器"""
        if self._running:
            logger.warning("Event bridge is already running")
            return
            
        self._running = True
        self._worker_task = asyncio.create_task(self._event_worker())
        logger.info("Event bridge started")
        
    async def stop(self) -> None:
        """停止事件桥接器"""
        if not self._running:
            return
            
        self._running = False
        
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Event bridge stopped")
        
    async def _event_worker(self) -> None:
        """事件处理工作线程"""
        logger.info("Event worker started")
        
        while self._running:
            try:
                # 等待事件，设置超时避免阻塞
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._process_event(event)
                self._event_queue.task_done()
                
            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except Exception as e:
                logger.error(f"Error in event worker: {e}")
                self._stats['events_failed'] += 1
                
        logger.info("Event worker stopped")
        
    async def _process_event(self, event: Event) -> None:
        """
        处理单个事件
        
        Args:
            event: 事件对象
        """
        try:
            # 处理同步处理器
            handlers = self._handlers.get(event.type, [])
            for handler in handlers:
                try:
                    if handler.can_handle(event.type):
                        await handler.handle(event)
                except Exception as e:
                    logger.error(f"Error in handler {handler.name}: {e}")
                    
            # 处理异步处理器
            async_handlers = self._async_handlers.get(event.type, [])
            for handler in async_handlers:
                try:
                    await handler(event)
                except Exception as e:
                    logger.error(f"Error in async handler: {e}")
                    
            self._stats['events_processed'] += 1
            
        except Exception as e:
            logger.error(f"Error processing event {event.type.value}: {e}")
            self._stats['events_failed'] += 1
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
        
    def clear_stats(self) -> None:
        """清除统计信息"""
        self._stats = {
            'events_processed': 0,
            'events_failed': 0,
            'handlers_count': len(self._handlers) + len(self._async_handlers)
        }


class WtpyEventAdapter:
    """wtpy事件适配器"""
    
    def __init__(self, event_bridge: EventBridge):
        self.event_bridge = event_bridge
        
    def on_tick(self, ctx: Any, tick: Any) -> None:
        """Tick数据回调"""
        self.event_bridge.emit_sync(
            EventType.TICK,
            tick,
            source="wtpy",
            symbol=getattr(tick, 'symbol', '')
        )
        
    def on_bar(self, ctx: Any, bar: Any) -> None:
        """K线数据回调"""
        self.event_bridge.emit_sync(
            EventType.BAR,
            bar,
            source="wtpy",
            symbol=getattr(bar, 'symbol', '')
        )
        
    def on_order(self, ctx: Any, order: Any) -> None:
        """订单回调"""
        self.event_bridge.emit_sync(
            EventType.ORDER,
            order,
            source="wtpy",
            symbol=getattr(order, 'symbol', '')
        )
        
    def on_trade(self, ctx: Any, trade: Any) -> None:
        """成交回调"""
        self.event_bridge.emit_sync(
            EventType.TRADE,
            trade,
            source="wtpy",
            symbol=getattr(trade, 'symbol', '')
        )
        
    def on_position(self, ctx: Any, position: Any) -> None:
        """持仓回调"""
        self.event_bridge.emit_sync(
            EventType.POSITION,
            position,
            source="wtpy",
            symbol=getattr(position, 'symbol', '')
        )


# 全局事件桥接器实例
_global_event_bridge: Optional[EventBridge] = None


def get_event_bridge() -> EventBridge:
    """获取全局事件桥接器实例"""
    global _global_event_bridge
    if _global_event_bridge is None:
        _global_event_bridge = EventBridge()
    return _global_event_bridge


def set_event_bridge(bridge: EventBridge) -> None:
    """设置全局事件桥接器实例"""
    global _global_event_bridge
    _global_event_bridge = bridge
