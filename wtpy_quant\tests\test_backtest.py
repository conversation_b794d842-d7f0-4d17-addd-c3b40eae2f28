"""
回测模块测试
"""

import pytest
import asyncio
import tempfile
import os
import pandas as pd
from pathlib import Path
from unittest.mock import MagicMock, patch, AsyncMock

from wtpy_quant.backtest import (
    BacktestEngine, BacktestTask, BacktestResult,
    ReportGenerator
)
from wtpy_quant.core.config_manager import BacktestConfig


@pytest.fixture
def temp_dir():
    """临时目录fixture"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def backtest_config(temp_dir):
    """回测配置fixture"""
    return BacktestConfig(
        data_path=str(temp_dir / "data"),
        output_path=str(temp_dir / "output"),
        strategy_path=str(temp_dir / "strategies")
    )


@pytest.fixture
def sample_performance_data(temp_dir):
    """样例业绩数据fixture"""
    # 创建样例业绩数据
    dates = pd.date_range('20230101', '20231231', freq='D')
    data = {
        'date': [d.strftime('%Y%m%d') for d in dates],
        'total_asset': [1000000 * (1 + 0.0001 * i + 0.01 * (i % 30 - 15) / 15) for i in range(len(dates))],
        'daily_return': [0.0001 + 0.01 * ((i % 30 - 15) / 15) for i in range(len(dates))]
    }
    
    df = pd.DataFrame(data)
    performance_file = temp_dir / "performance.csv"
    df.to_csv(performance_file, index=False)
    
    return str(performance_file)


@pytest.fixture
def sample_trades_data(temp_dir):
    """样例交易数据fixture"""
    data = {
        'symbol': ['000001.SZ', '000002.SZ', '600000.SH'] * 10,
        'side': ['buy', 'sell'] * 15,
        'quantity': [1000, 2000, 1500] * 10,
        'price': [10.0 + i * 0.1 for i in range(30)],
        'profit': [100 * (i % 5 - 2) for i in range(30)]  # 有正有负的盈亏
    }
    
    df = pd.DataFrame(data)
    trades_file = temp_dir / "trades.csv"
    df.to_csv(trades_file, index=False)
    
    return str(trades_file)


class TestBacktestEngine:
    """回测引擎测试"""
    
    def test_initialization(self, backtest_config):
        """测试初始化"""
        engine = BacktestEngine(backtest_config)
        
        assert engine.config == backtest_config
        assert len(engine._tasks) == 0
        assert len(engine._results) == 0
        assert engine._stats['total_tasks'] == 0
        
        # 验证输出目录已创建
        assert os.path.exists(backtest_config.output_path)
        
    @pytest.mark.asyncio
    async def test_create_task(self, backtest_config):
        """测试创建任务"""
        engine = BacktestEngine(backtest_config)
        
        task_id = await engine.create_task(
            name="测试策略回测",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0
        )
        
        assert task_id is not None
        assert task_id in engine._tasks
        assert engine._stats['total_tasks'] == 1
        
        # 验证任务信息
        task = engine._tasks[task_id]
        assert task.name == "测试策略回测"
        assert task.strategy_name == "TestStrategy"
        assert task.start_date == "20230101"
        assert task.end_date == "20231231"
        assert task.initial_capital == 1000000.0
        assert task.status == "pending"
        
    @pytest.mark.asyncio
    async def test_run_task_without_wtpy(self, backtest_config):
        """测试运行任务（不依赖wtpy）"""
        engine = BacktestEngine(backtest_config)
        
        # 创建任务
        task_id = await engine.create_task(
            name="测试策略回测",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231"
        )
        
        # Mock wtpy不可用的情况
        with patch('wtpy_quant.backtest.backtest_engine.BacktestEngine._run_wtpy_backtest', 
                  return_value=False):
            success = await engine.run_task(task_id)
            assert success  # 任务启动成功
            
            # 等待任务完成
            await asyncio.sleep(0.1)
            
            task = engine._tasks[task_id]
            assert task.status == "failed"
            assert "wtpy backtest execution failed" in task.error_message
            
    def test_get_task(self, backtest_config):
        """测试获取任务"""
        engine = BacktestEngine(backtest_config)
        
        # 不存在的任务
        task = engine.get_task("nonexistent")
        assert task is None
        
    def test_list_tasks(self, backtest_config):
        """测试列出任务"""
        engine = BacktestEngine(backtest_config)
        
        # 空列表
        tasks = engine.list_tasks()
        assert len(tasks) == 0
        
        # 按状态过滤
        pending_tasks = engine.list_tasks(status="pending")
        assert len(pending_tasks) == 0
        
    def test_generate_backtest_config(self, backtest_config):
        """测试生成回测配置"""
        engine = BacktestEngine(backtest_config)
        
        task = BacktestTask(
            task_id="test_task",
            name="测试任务",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0,
            config={"custom_param": "value"}
        )
        
        bt_config = engine._generate_backtest_config(task, "/tmp/output")
        
        assert bt_config['backtest']['start_date'] == 20230101
        assert bt_config['backtest']['end_date'] == 20231231
        assert bt_config['backtest']['init_capital'] == 1000000.0
        assert bt_config['backtest']['strategy'] == "TestStrategy"
        assert bt_config['custom_param'] == "value"  # 用户配置已合并
        
    @pytest.mark.asyncio
    async def test_analyze_backtest_result(self, backtest_config, sample_performance_data, temp_dir):
        """测试分析回测结果"""
        engine = BacktestEngine(backtest_config)
        
        task = BacktestTask(
            task_id="test_task",
            name="测试任务",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0
        )
        
        # 创建输出目录
        output_dir = temp_dir / "test_output"
        output_dir.mkdir()
        
        # 复制样例数据到输出目录
        import shutil
        shutil.copy(sample_performance_data, output_dir / "performance.csv")
        
        result = await engine._analyze_backtest_result(task, str(output_dir))
        
        assert result is not None
        assert result.task_id == "test_task"
        assert result.strategy_name == "TestStrategy"
        assert result.initial_capital == 1000000.0
        assert result.final_capital > 0
        assert result.total_return != 0
        assert result.performance_file == str(output_dir / "performance.csv")


class TestReportGenerator:
    """报告生成器测试"""
    
    def test_initialization(self):
        """测试初始化"""
        generator = ReportGenerator()
        assert generator.template_path is not None
        
    @pytest.mark.asyncio
    async def test_prepare_report_data(self, sample_performance_data, sample_trades_data, temp_dir):
        """测试准备报告数据"""
        generator = ReportGenerator()
        
        task = BacktestTask(
            task_id="test_task",
            name="测试任务",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0
        )
        
        result = BacktestResult(
            task_id="test_task",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0,
            final_capital=1100000.0,
            total_return=0.10,
            annualized_return=0.10,
            max_drawdown=0.05,
            sharpe_ratio=1.5,
            calmar_ratio=2.0,
            total_trades=100,
            win_rate=0.6,
            profit_loss_ratio=1.2,
            performance_file=sample_performance_data,
            trades_file=sample_trades_data
        )
        
        report_data = await generator._prepare_report_data(task, result)
        
        assert 'basic_info' in report_data
        assert 'performance_metrics' in report_data
        assert 'trading_stats' in report_data
        assert 'detailed_analysis' in report_data
        
        # 验证基本信息
        basic_info = report_data['basic_info']
        assert basic_info['task_id'] == "test_task"
        assert basic_info['strategy_name'] == "TestStrategy"
        
        # 验证业绩指标
        performance_metrics = report_data['performance_metrics']
        assert 'total_return' in performance_metrics
        assert 'sharpe_ratio' in performance_metrics
        
    def test_calculate_monthly_returns(self, sample_performance_data):
        """测试计算月度收益"""
        generator = ReportGenerator()
        
        df = pd.read_csv(sample_performance_data)
        monthly_returns = generator._calculate_monthly_returns(df)
        
        assert len(monthly_returns) > 0
        assert 'month' in monthly_returns[0]
        assert 'return' in monthly_returns[0]
        assert 'return_value' in monthly_returns[0]
        
    def test_analyze_trades(self, sample_trades_data):
        """测试分析交易"""
        generator = ReportGenerator()
        
        df = pd.read_csv(sample_trades_data)
        trade_analysis = generator._analyze_trades(df)
        
        assert 'total_trades' in trade_analysis
        assert 'profitable_trades' in trade_analysis
        assert 'losing_trades' in trade_analysis
        assert 'win_rate' in trade_analysis
        assert 'symbol_stats' in trade_analysis
        
        # 验证按股票统计
        symbol_stats = trade_analysis['symbol_stats']
        assert len(symbol_stats) > 0
        assert 'symbol' in symbol_stats[0]
        assert 'trades' in symbol_stats[0]
        assert 'total_profit' in symbol_stats[0]
        
    @pytest.mark.asyncio
    async def test_create_nav_chart(self, sample_performance_data):
        """测试创建净值曲线图"""
        generator = ReportGenerator()
        
        chart_base64 = await generator._create_nav_chart(sample_performance_data)
        
        # 如果matplotlib可用，应该返回base64编码的图片
        if chart_base64:
            assert isinstance(chart_base64, str)
            assert len(chart_base64) > 0
            
    @pytest.mark.asyncio
    async def test_create_drawdown_chart(self, sample_performance_data):
        """测试创建回撤图"""
        generator = ReportGenerator()
        
        chart_base64 = await generator._create_drawdown_chart(sample_performance_data)
        
        # 如果matplotlib可用，应该返回base64编码的图片
        if chart_base64:
            assert isinstance(chart_base64, str)
            assert len(chart_base64) > 0
            
    @pytest.mark.asyncio
    async def test_generate_html_report(self, sample_performance_data, sample_trades_data, temp_dir):
        """测试生成HTML报告"""
        generator = ReportGenerator()
        
        task = BacktestTask(
            task_id="test_task",
            name="测试任务",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0
        )
        
        result = BacktestResult(
            task_id="test_task",
            strategy_name="TestStrategy",
            start_date="20230101",
            end_date="20231231",
            initial_capital=1000000.0,
            final_capital=1100000.0,
            total_return=0.10,
            annualized_return=0.10,
            max_drawdown=0.05,
            sharpe_ratio=1.5,
            calmar_ratio=2.0,
            total_trades=100,
            win_rate=0.6,
            profit_loss_ratio=1.2,
            performance_file=sample_performance_data,
            trades_file=sample_trades_data
        )
        
        output_path = str(temp_dir)
        html_file = await generator.generate_html_report(task, result, output_path)
        
        assert html_file is not None
        assert os.path.exists(html_file)
        assert html_file.endswith('.html')
        
        # 验证HTML内容
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            assert '测试任务' in content
            assert 'TestStrategy' in content
            assert '10.00%' in content  # 总收益率


if __name__ == "__main__":
    pytest.main([__file__])
