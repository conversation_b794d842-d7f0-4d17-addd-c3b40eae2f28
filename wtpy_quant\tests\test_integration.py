"""
集成测试 - 测试各模块间的协作
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock, patch
from datetime import date, datetime

from wtpy_quant.core.engine import WtpyEngine
from wtpy_quant.core.config_manager import SystemConfig, EngineConfig, DataConfig, BacktestConfig
from wtpy_quant.strategy import ConceptStrategy, MicrostructureStrategy
from wtpy_quant.backtest.backtest_engine import BacktestEngine, BacktestTask
from wtpy_quant.data.concept_manager import ConceptManager


@pytest.fixture
def temp_data_dir():
    """临时数据目录"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def test_config(temp_data_dir):
    """测试配置"""
    return SystemConfig(
        engine=EngineConfig(
            mode="backtest",
            data_path=temp_data_dir
        ),
        data=DataConfig(
            path=temp_data_dir,
            source="mock"
        ),
        backtest=BacktestConfig(
            data_path=temp_data_dir
        )
    )


class TestEngineIntegration:
    """引擎集成测试"""
    
    def test_engine_lifecycle(self, test_config):
        """测试引擎生命周期"""
        engine = WtpyEngine(test_config.engine)
        
        # 测试初始化
        assert engine.config == test_config.engine
        assert engine.strategy_manager is not None
        assert engine.event_bridge is not None
        
        # 测试启动和停止
        # 注意：实际的wtpy引擎需要mock
        with patch.object(engine, '_init_wtpy_engine'):
            engine.start()
            assert engine.is_running()
            
            engine.stop()
            assert not engine.is_running()
            
    def test_strategy_registration_and_execution(self, test_config):
        """测试策略注册和执行"""
        engine = WtpyEngine(test_config.engine)
        
        # 创建测试策略
        strategy = ConceptStrategy(
            name="TestConceptStrategy",
            max_positions=5,
            position_size=0.2
        )
        
        # 注册策略
        strategy_id = engine.strategy_manager.register_strategy(strategy)
        assert strategy_id is not None
        
        # 获取策略信息
        strategy_info = engine.strategy_manager.get_strategy_info(strategy_id)
        assert strategy_info.name == "TestConceptStrategy"
        assert strategy_info.strategy_type == "ConceptStrategy"
        
        # 启动策略
        with patch.object(engine.strategy_manager, '_start_strategy_impl', return_value=True):
            success = engine.strategy_manager.start_strategy(strategy_id)
            assert success
            
        # 停止策略
        with patch.object(engine.strategy_manager, '_stop_strategy_impl', return_value=True):
            success = engine.strategy_manager.stop_strategy(strategy_id)
            assert success


class TestBacktestIntegration:
    """回测集成测试"""
    
    @pytest.mark.asyncio
    async def test_backtest_workflow(self, test_config):
        """测试完整回测流程"""
        backtest_engine = BacktestEngine(test_config.backtest)
        
        # 创建回测任务
        task = BacktestTask(
            name="集成测试回测",
            strategy_type="ConceptStrategy",
            strategy_params={
                "max_positions": 10,
                "position_size": 0.1
            },
            start_date=date(2023, 1, 1),
            end_date=date(2023, 3, 31),
            initial_capital=100000.0,
            symbols=["000001.SZ", "000002.SZ"]
        )
        
        # 提交任务
        task_id = await backtest_engine.submit_task(task)
        assert task_id is not None
        
        # 获取任务信息
        retrieved_task = backtest_engine.get_task(task_id)
        assert retrieved_task is not None
        assert retrieved_task.name == "集成测试回测"
        assert retrieved_task.status == "pending"
        
        # Mock回测执行
        with patch.object(backtest_engine, '_run_backtest_impl') as mock_run:
            mock_result = MagicMock()
            mock_result.performance_metrics = {
                'total_return': 0.15,
                'sharpe_ratio': 1.2,
                'max_drawdown': 0.05
            }
            mock_run.return_value = mock_result
            
            # 运行回测
            result = await backtest_engine.run_task(task_id)
            assert result is not None
            assert result.performance_metrics['total_return'] == 0.15
            
        # 检查任务状态
        completed_task = backtest_engine.get_task(task_id)
        assert completed_task.status == "completed"
        
    @pytest.mark.asyncio
    async def test_multiple_backtest_tasks(self, test_config):
        """测试多个回测任务"""
        backtest_engine = BacktestEngine(test_config.backtest)
        
        # 创建多个任务
        tasks = []
        for i in range(3):
            task = BacktestTask(
                name=f"回测任务{i+1}",
                strategy_type="ConceptStrategy",
                strategy_params={"max_positions": 5 + i},
                start_date=date(2023, 1, 1),
                end_date=date(2023, 2, 28),
                initial_capital=100000.0
            )
            task_id = await backtest_engine.submit_task(task)
            tasks.append(task_id)
            
        # 检查任务列表
        all_tasks = backtest_engine.list_tasks()
        assert len(all_tasks) >= 3
        
        for task_id in tasks:
            assert task_id in all_tasks


class TestDataIntegration:
    """数据层集成测试"""
    
    @pytest.mark.asyncio
    async def test_concept_manager_integration(self):
        """测试概念管理器集成"""
        concept_manager = ConceptManager("mock")
        
        # Mock初始化
        with patch.object(concept_manager, '_load_concept_data'):
            await concept_manager.initialize()
            
        # Mock概念数据
        mock_concepts = [
            MagicMock(concept_code="AI", concept_name="人工智能", change_pct=0.05),
            MagicMock(concept_code="5G", concept_name="5G概念", change_pct=0.03)
        ]
        
        with patch.object(concept_manager, 'get_top_concepts', return_value=mock_concepts):
            top_concepts = concept_manager.get_top_concepts(limit=10)
            assert len(top_concepts) == 2
            assert top_concepts[0].concept_code == "AI"
            
    def test_data_flow_integration(self, test_config):
        """测试数据流集成"""
        from wtpy_quant.data.market_adapter import MarketAdapter
        from wtpy_quant.data.mmap_store import MmapStore
        
        # 创建数据适配器
        adapter = MarketAdapter("mock")
        
        # 创建内存存储
        store = MmapStore(test_config.data.path)
        
        # Mock数据流
        mock_tick = MagicMock()
        mock_tick.symbol = "000001.SZ"
        mock_tick.price = 10.50
        mock_tick.volume = 1000
        mock_tick.timestamp = datetime.now()
        
        # 测试数据存储
        with patch.object(store, 'store_tick') as mock_store:
            # 模拟数据回调
            if hasattr(adapter, '_on_tick'):
                adapter._on_tick(mock_tick)
                
            # 验证存储被调用
            # mock_store.assert_called_once()


class TestStrategyIntegration:
    """策略集成测试"""
    
    def test_concept_strategy_integration(self, test_config):
        """测试概念策略集成"""
        strategy = ConceptStrategy(
            name="集成测试概念策略",
            max_positions=8,
            position_size=0.15,
            stop_loss=-0.03
        )
        
        # 测试策略参数
        assert strategy.max_positions == 8
        assert strategy.position_size == 0.15
        assert strategy.stop_loss == -0.03
        
        # Mock上下文
        mock_context = MagicMock()
        
        # 测试初始化
        with patch.object(strategy, '_subscribe_data'):
            strategy.on_init(mock_context)
            assert strategy._initialized
            
        # 测试数据处理
        mock_tick = MagicMock()
        mock_tick.price = 10.50
        
        strategy.on_tick(mock_context, "000001.SZ", mock_tick)
        assert "000001.SZ" in strategy._tick_cache
        
    def test_microstructure_strategy_integration(self, test_config):
        """测试微结构策略集成"""
        strategy = MicrostructureStrategy(
            name="集成测试微结构策略",
            target_symbols=["000001.SZ", "000002.SZ"],
            tick_window=50,
            signal_threshold=0.7
        )
        
        # 测试策略参数
        assert len(strategy.target_symbols) == 2
        assert strategy.tick_window == 50
        assert strategy.signal_threshold == 0.7
        
        # Mock上下文
        mock_context = MagicMock()
        
        # 测试初始化
        strategy.on_init(mock_context)
        assert strategy._initialized
        assert len(strategy.tick_buffers) == 2
        
        # 测试Tick数据处理
        mock_tick = MagicMock()
        mock_tick.actiontime = 1640995200
        mock_tick.price = 10.50
        mock_tick.volume = 1000
        mock_tick.bidprice = [10.49]
        mock_tick.askprice = [10.51]
        mock_tick.bidqty = [500]
        mock_tick.askqty = [800]
        
        strategy.on_tick(mock_context, "000001.SZ", mock_tick)
        assert len(strategy.tick_buffers["000001.SZ"]) == 1


class TestAPIIntegration:
    """API集成测试"""
    
    @pytest.mark.asyncio
    async def test_api_strategy_workflow(self):
        """测试API策略工作流"""
        from fastapi.testclient import TestClient
        from wtpy_quant.api.main import app
        
        client = TestClient(app)
        
        # Mock应用状态
        with patch('wtpy_quant.api.main.app_state') as mock_state:
            mock_state['initialized'] = True
            mock_state['engine'] = MagicMock()
            mock_strategy_manager = MagicMock()
            mock_state['engine'].strategy_manager = mock_strategy_manager
            
            # 测试创建策略
            create_request = {
                "name": "API测试策略",
                "type": "ConceptStrategy",
                "parameters": {"max_positions": 10}
            }
            
            mock_strategy_manager.register_strategy.return_value = "test_strategy_id"
            mock_strategy_info = MagicMock()
            mock_strategy_info.name = "API测试策略"
            mock_strategy_info.strategy_type = "ConceptStrategy"
            mock_strategy_info.status.value = "inactive"
            mock_strategy_info.description = None
            mock_strategy_info.parameters = {"max_positions": 10}
            mock_strategy_info.create_time = "2023-01-01T00:00:00"
            mock_strategy_info.update_time = "2023-01-01T00:00:00"
            mock_strategy_manager.get_strategy_info.return_value = mock_strategy_info
            
            response = client.post("/api/v1/strategy/", json=create_request)
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "API测试策略"
            
            # 测试启动策略
            mock_strategy_manager.start_strategy.return_value = True
            control_request = {"action": "start"}
            response = client.post("/api/v1/strategy/test_strategy_id/control", json=control_request)
            assert response.status_code == 200


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
