"""
Dynamic loader utilities to import strategy classes/functions by dotted path or file path.
Supports formats:
- "package.module:ClassName"
- "/abs/or/relative/path/to/module.py:ClassName"
- "package.module" (returns module)
- "/path/to/module.py" (returns module)
"""
from __future__ import annotations
import importlib
import importlib.util
from pathlib import Path
from types import ModuleType
from typing import Any, Optional


def _load_module_from_file(file_path: str | Path, module_name: Optional[str] = None) -> ModuleType:
    file_path = str(file_path)
    name = module_name or Path(file_path).stem
    spec = importlib.util.spec_from_file_location(name, file_path)
    if spec is None or spec.loader is None:
        raise ImportError(f"Cannot import module from file: {file_path}")
    mod = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mod)  # type: ignore[arg-type]
    return mod


def load(dotted_or_file: str) -> Any:
    if ":" in dotted_or_file:
        target, attr = dotted_or_file.split(":", 1)
    else:
        target, attr = dotted_or_file, None

    if target.endswith(".py") or "/" in target or "\\" in target:
        mod = _load_module_from_file(target)
    else:
        mod = importlib.import_module(target)

    if attr:
        if not hasattr(mod, attr):
            raise ImportError(f"Attribute {attr} not found in {target}")
        return getattr(mod, attr)
    return mod

