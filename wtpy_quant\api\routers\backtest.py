"""
回测管理API路由

提供回测相关的REST API接口，包括：
- 回测任务创建和管理
- 回测结果查询
- 回测报告生成
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import asyncio

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from ...backtest.backtest_engine import BacktestEngine, BacktestTask, BacktestResult
from ...backtest.report_generator import ReportGenerator

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic模型定义
class BacktestCreateRequest(BaseModel):
    """创建回测请求"""
    name: str = Field(..., description="回测任务名称")
    strategy_type: str = Field(..., description="策略类型")
    strategy_params: Dict[str, Any] = Field(default_factory=dict, description="策略参数")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")
    initial_capital: float = Field(100000.0, description="初始资金")
    symbols: List[str] = Field(default_factory=list, description="股票池")
    benchmark: Optional[str] = Field("000300.SH", description="基准指数")
    description: Optional[str] = Field(None, description="回测描述")


class BacktestInfo(BaseModel):
    """回测任务信息"""
    task_id: str
    name: str
    strategy_type: str
    status: str
    start_date: str
    end_date: str
    initial_capital: float
    create_time: str
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    progress: float = 0.0
    error_message: Optional[str] = None


class BacktestResultSummary(BaseModel):
    """回测结果摘要"""
    task_id: str
    total_return: float
    annual_return: float
    max_drawdown: float
    sharpe_ratio: float
    calmar_ratio: float
    win_rate: float
    total_trades: int
    benchmark_return: Optional[float] = None


# 依赖注入
def get_backtest_engine() -> BacktestEngine:
    """获取回测引擎"""
    from ...api.main import app_state
    if not app_state['initialized'] or not app_state['backtest_engine']:
        raise HTTPException(status_code=503, detail="Backtest engine not available")
    return app_state['backtest_engine']


@router.get("/", response_model=List[BacktestInfo])
async def list_backtests(
    status: Optional[str] = None,
    strategy_type: Optional[str] = None,
    limit: int = 50,
    offset: int = 0,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测任务列表
    
    Args:
        status: 状态过滤 (pending, running, completed, failed)
        strategy_type: 策略类型过滤
        limit: 返回数量限制
        offset: 偏移量
    """
    try:
        tasks = backtest_engine.list_tasks()
        
        # 转换为API模型并应用过滤
        task_list = []
        for task_id, task in tasks.items():
            # 应用过滤条件
            if status and task.status != status:
                continue
            if strategy_type and task.strategy_type != strategy_type:
                continue
                
            api_info = BacktestInfo(
                task_id=task_id,
                name=task.name,
                strategy_type=task.strategy_type,
                status=task.status,
                start_date=task.start_date.isoformat(),
                end_date=task.end_date.isoformat(),
                initial_capital=task.initial_capital,
                create_time=task.create_time.isoformat(),
                start_time=task.start_time.isoformat() if task.start_time else None,
                end_time=task.end_time.isoformat() if task.end_time else None,
                progress=task.progress,
                error_message=task.error_message
            )
            task_list.append(api_info)
            
        # 排序和分页
        task_list.sort(key=lambda x: x.create_time, reverse=True)
        return task_list[offset:offset + limit]
        
    except Exception as e:
        logger.error(f"Error listing backtests: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=BacktestInfo)
async def create_backtest(
    request: BacktestCreateRequest,
    background_tasks: BackgroundTasks,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    创建回测任务
    """
    try:
        # 验证日期
        if request.start_date >= request.end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")
            
        # 创建回测任务
        task = BacktestTask(
            name=request.name,
            strategy_type=request.strategy_type,
            strategy_params=request.strategy_params,
            start_date=request.start_date,
            end_date=request.end_date,
            initial_capital=request.initial_capital,
            symbols=request.symbols,
            benchmark=request.benchmark,
            description=request.description
        )
        
        # 提交任务
        task_id = await backtest_engine.submit_task(task)
        
        # 在后台运行回测
        background_tasks.add_task(run_backtest_task, backtest_engine, task_id)
        
        return BacktestInfo(
            task_id=task_id,
            name=task.name,
            strategy_type=task.strategy_type,
            status=task.status,
            start_date=task.start_date.isoformat(),
            end_date=task.end_date.isoformat(),
            initial_capital=task.initial_capital,
            create_time=task.create_time.isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error creating backtest: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def run_backtest_task(backtest_engine: BacktestEngine, task_id: str):
    """在后台运行回测任务"""
    try:
        await backtest_engine.run_task(task_id)
    except Exception as e:
        logger.error(f"Error running backtest task {task_id}: {e}")


@router.get("/{task_id}", response_model=BacktestInfo)
async def get_backtest(
    task_id: str,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测任务详情
    """
    try:
        task = backtest_engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Backtest task not found")
            
        return BacktestInfo(
            task_id=task_id,
            name=task.name,
            strategy_type=task.strategy_type,
            status=task.status,
            start_date=task.start_date.isoformat(),
            end_date=task.end_date.isoformat(),
            initial_capital=task.initial_capital,
            create_time=task.create_time.isoformat(),
            start_time=task.start_time.isoformat() if task.start_time else None,
            end_time=task.end_time.isoformat() if task.end_time else None,
            progress=task.progress,
            error_message=task.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{task_id}")
async def delete_backtest(
    task_id: str,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    删除回测任务
    """
    try:
        task = backtest_engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Backtest task not found")
            
        # 如果任务正在运行，先停止
        if task.status == "running":
            await backtest_engine.cancel_task(task_id)
            
        # 删除任务
        success = backtest_engine.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to delete backtest task")
            
        return {"message": f"Backtest task {task_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting backtest {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{task_id}/cancel")
async def cancel_backtest(
    task_id: str,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    取消回测任务
    """
    try:
        task = backtest_engine.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Backtest task not found")
            
        if task.status not in ["pending", "running"]:
            raise HTTPException(status_code=400, detail="Task cannot be cancelled")
            
        success = await backtest_engine.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to cancel backtest task")
            
        return {"message": f"Backtest task {task_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling backtest {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/result", response_model=BacktestResultSummary)
async def get_backtest_result(
    task_id: str,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测结果摘要
    """
    try:
        result = backtest_engine.get_result(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="Backtest result not found")
            
        return BacktestResultSummary(
            task_id=task_id,
            total_return=result.performance_metrics.get('total_return', 0.0),
            annual_return=result.performance_metrics.get('annual_return', 0.0),
            max_drawdown=result.performance_metrics.get('max_drawdown', 0.0),
            sharpe_ratio=result.performance_metrics.get('sharpe_ratio', 0.0),
            calmar_ratio=result.performance_metrics.get('calmar_ratio', 0.0),
            win_rate=result.performance_metrics.get('win_rate', 0.0),
            total_trades=result.performance_metrics.get('total_trades', 0),
            benchmark_return=result.performance_metrics.get('benchmark_return')
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest result {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/result/detail")
async def get_backtest_result_detail(
    task_id: str,
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测结果详情
    """
    try:
        result = backtest_engine.get_result(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="Backtest result not found")
            
        return {
            "task_id": task_id,
            "performance_metrics": result.performance_metrics,
            "trade_records": result.trade_records,
            "daily_returns": result.daily_returns,
            "positions": result.positions,
            "benchmark_data": result.benchmark_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest result detail {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{task_id}/report")
async def get_backtest_report(
    task_id: str,
    format: str = "html",
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测报告
    
    Args:
        format: 报告格式 (html, pdf)
    """
    try:
        result = backtest_engine.get_result(task_id)
        if not result:
            raise HTTPException(status_code=404, detail="Backtest result not found")
            
        # 生成报告
        report_generator = ReportGenerator()
        
        if format.lower() == "html":
            report_content = await report_generator.generate_html_report(result)
            return {"content": report_content, "format": "html"}
        elif format.lower() == "pdf":
            # PDF生成需要额外的库支持
            raise HTTPException(status_code=501, detail="PDF format not implemented yet")
        else:
            raise HTTPException(status_code=400, detail="Unsupported format")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating backtest report {task_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/summary")
async def get_backtest_stats(
    backtest_engine: BacktestEngine = Depends(get_backtest_engine)
):
    """
    获取回测统计信息
    """
    try:
        tasks = backtest_engine.list_tasks()
        
        stats = {
            "total_tasks": len(tasks),
            "pending_tasks": len([t for t in tasks.values() if t.status == "pending"]),
            "running_tasks": len([t for t in tasks.values() if t.status == "running"]),
            "completed_tasks": len([t for t in tasks.values() if t.status == "completed"]),
            "failed_tasks": len([t for t in tasks.values() if t.status == "failed"]),
            "strategy_types": {}
        }
        
        # 统计策略类型分布
        for task in tasks.values():
            strategy_type = task.strategy_type
            if strategy_type not in stats["strategy_types"]:
                stats["strategy_types"][strategy_type] = 0
            stats["strategy_types"][strategy_type] += 1
            
        return stats
        
    except Exception as e:
        logger.error(f"Error getting backtest stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))
