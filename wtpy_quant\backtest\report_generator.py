"""
回测报告生成器 - 生成详细的回测分析报告

负责生成回测报告，包括：
- HTML格式的可视化报告
- PDF格式的正式报告
- 图表生成与分析
- 风险指标计算
"""

import os
import json
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.font_manager import FontProperties
import seaborn as sns
from jinja2 import Template
import base64
from io import BytesIO

from .backtest_engine import BacktestResult, BacktestTask

logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class ReportGenerator:
    """回测报告生成器"""
    
    def __init__(self, template_path: Optional[str] = None):
        """
        初始化报告生成器
        
        Args:
            template_path: 模板文件路径
        """
        self.template_path = template_path or self._get_default_template_path()
        
        # 图表样式设置
        sns.set_style("whitegrid")
        plt.style.use('seaborn-v0_8')
        
    def _get_default_template_path(self) -> str:
        """获取默认模板路径"""
        current_dir = os.path.dirname(__file__)
        return os.path.join(current_dir, "templates", "report_template.html")
        
    async def generate_html_report(self, 
                                 task: BacktestTask, 
                                 result: BacktestResult,
                                 output_path: str) -> str:
        """
        生成HTML报告
        
        Args:
            task: 回测任务
            result: 回测结果
            output_path: 输出路径
            
        Returns:
            str: 报告文件路径
        """
        try:
            logger.info(f"Generating HTML report for task {task.task_id}")
            
            # 准备报告数据
            report_data = await self._prepare_report_data(task, result)
            
            # 生成图表
            charts = await self._generate_charts(result, output_path)
            report_data['charts'] = charts
            
            # 渲染HTML模板
            html_content = self._render_html_template(report_data)
            
            # 保存HTML文件
            html_file = os.path.join(output_path, f"report_{task.task_id}.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
                
            logger.info(f"HTML report generated: {html_file}")
            return html_file
            
        except Exception as e:
            logger.error(f"Error generating HTML report: {e}")
            raise
            
    async def _prepare_report_data(self, task: BacktestTask, result: BacktestResult) -> Dict[str, Any]:
        """
        准备报告数据
        
        Args:
            task: 回测任务
            result: 回测结果
            
        Returns:
            Dict[str, Any]: 报告数据
        """
        # 基本信息
        basic_info = {
            'task_id': task.task_id,
            'task_name': task.name,
            'strategy_name': task.strategy_name,
            'start_date': task.start_date,
            'end_date': task.end_date,
            'initial_capital': f"{result.initial_capital:,.2f}",
            'final_capital': f"{result.final_capital:,.2f}",
            'generate_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 收益指标
        performance_metrics = {
            'total_return': f"{result.total_return:.2%}",
            'annualized_return': f"{result.annualized_return:.2%}",
            'max_drawdown': f"{result.max_drawdown:.2%}",
            'sharpe_ratio': f"{result.sharpe_ratio:.3f}",
            'calmar_ratio': f"{result.calmar_ratio:.3f}"
        }
        
        # 交易统计
        trading_stats = {
            'total_trades': result.total_trades,
            'win_rate': f"{result.win_rate:.2%}",
            'profit_loss_ratio': f"{result.profit_loss_ratio:.3f}"
        }
        
        # 详细分析
        detailed_analysis = await self._analyze_detailed_performance(result)
        
        return {
            'basic_info': basic_info,
            'performance_metrics': performance_metrics,
            'trading_stats': trading_stats,
            'detailed_analysis': detailed_analysis
        }
        
    async def _analyze_detailed_performance(self, result: BacktestResult) -> Dict[str, Any]:
        """
        详细业绩分析
        
        Args:
            result: 回测结果
            
        Returns:
            Dict[str, Any]: 详细分析结果
        """
        analysis = {}
        
        try:
            # 读取业绩数据
            if result.performance_file and os.path.exists(result.performance_file):
                performance_df = pd.read_csv(result.performance_file)
                
                if not performance_df.empty:
                    # 月度收益分析
                    monthly_returns = self._calculate_monthly_returns(performance_df)
                    analysis['monthly_returns'] = monthly_returns
                    
                    # 年度收益分析
                    yearly_returns = self._calculate_yearly_returns(performance_df)
                    analysis['yearly_returns'] = yearly_returns
                    
                    # 回撤分析
                    drawdown_analysis = self._analyze_drawdowns(performance_df)
                    analysis['drawdown_analysis'] = drawdown_analysis
                    
            # 交易分析
            if result.trades_file and os.path.exists(result.trades_file):
                trades_df = pd.read_csv(result.trades_file)
                
                if not trades_df.empty:
                    trade_analysis = self._analyze_trades(trades_df)
                    analysis['trade_analysis'] = trade_analysis
                    
            # 持仓分析
            if result.positions_file and os.path.exists(result.positions_file):
                positions_df = pd.read_csv(result.positions_file)
                
                if not positions_df.empty:
                    position_analysis = self._analyze_positions(positions_df)
                    analysis['position_analysis'] = position_analysis
                    
        except Exception as e:
            logger.error(f"Error in detailed performance analysis: {e}")
            
        return analysis
        
    def _calculate_monthly_returns(self, performance_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """计算月度收益"""
        try:
            if 'date' not in performance_df.columns or 'total_asset' not in performance_df.columns:
                return []
                
            performance_df['date'] = pd.to_datetime(performance_df['date'], format='%Y%m%d')
            performance_df.set_index('date', inplace=True)
            
            # 按月重采样
            monthly_data = performance_df['total_asset'].resample('M').last()
            monthly_returns = monthly_data.pct_change().dropna()
            
            results = []
            for date, return_rate in monthly_returns.items():
                results.append({
                    'month': date.strftime('%Y-%m'),
                    'return': f"{return_rate:.2%}",
                    'return_value': return_rate
                })
                
            return results
            
        except Exception as e:
            logger.error(f"Error calculating monthly returns: {e}")
            return []
            
    def _calculate_yearly_returns(self, performance_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """计算年度收益"""
        try:
            if 'date' not in performance_df.columns or 'total_asset' not in performance_df.columns:
                return []
                
            performance_df['date'] = pd.to_datetime(performance_df['date'], format='%Y%m%d')
            performance_df.set_index('date', inplace=True)
            
            # 按年重采样
            yearly_data = performance_df['total_asset'].resample('Y').last()
            yearly_returns = yearly_data.pct_change().dropna()
            
            results = []
            for date, return_rate in yearly_returns.items():
                results.append({
                    'year': date.strftime('%Y'),
                    'return': f"{return_rate:.2%}",
                    'return_value': return_rate
                })
                
            return results
            
        except Exception as e:
            logger.error(f"Error calculating yearly returns: {e}")
            return []
            
    def _analyze_drawdowns(self, performance_df: pd.DataFrame) -> Dict[str, Any]:
        """分析回撤"""
        try:
            if 'total_asset' not in performance_df.columns:
                return {}
                
            cumulative = performance_df['total_asset']
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            
            # 最大回撤
            max_drawdown = abs(drawdown.min())
            
            # 回撤持续时间
            drawdown_periods = []
            in_drawdown = False
            start_idx = 0
            
            for i, dd in enumerate(drawdown):
                if dd < -0.01 and not in_drawdown:  # 开始回撤（超过1%）
                    in_drawdown = True
                    start_idx = i
                elif dd >= -0.001 and in_drawdown:  # 结束回撤
                    in_drawdown = False
                    duration = i - start_idx
                    max_dd_in_period = abs(drawdown[start_idx:i+1].min())
                    drawdown_periods.append({
                        'duration': duration,
                        'max_drawdown': max_dd_in_period
                    })
                    
            # 平均回撤持续时间
            avg_duration = np.mean([p['duration'] for p in drawdown_periods]) if drawdown_periods else 0
            
            return {
                'max_drawdown': f"{max_drawdown:.2%}",
                'drawdown_periods': len(drawdown_periods),
                'avg_duration': f"{avg_duration:.1f} days"
            }
            
        except Exception as e:
            logger.error(f"Error analyzing drawdowns: {e}")
            return {}
            
    def _analyze_trades(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """分析交易"""
        try:
            analysis = {}
            
            if 'profit' in trades_df.columns:
                profits = trades_df['profit']
                
                # 盈利交易统计
                profitable_trades = profits[profits > 0]
                losing_trades = profits[profits < 0]
                
                analysis.update({
                    'total_trades': len(trades_df),
                    'profitable_trades': len(profitable_trades),
                    'losing_trades': len(losing_trades),
                    'win_rate': f"{len(profitable_trades) / len(trades_df):.2%}" if len(trades_df) > 0 else "0%",
                    'avg_profit': f"{profitable_trades.mean():.2f}" if len(profitable_trades) > 0 else "0",
                    'avg_loss': f"{losing_trades.mean():.2f}" if len(losing_trades) > 0 else "0",
                    'max_profit': f"{profits.max():.2f}",
                    'max_loss': f"{profits.min():.2f}"
                })
                
            # 按股票统计
            if 'symbol' in trades_df.columns:
                symbol_stats = trades_df.groupby('symbol').agg({
                    'profit': ['count', 'sum', 'mean']
                }).round(2)
                
                # 转换为列表格式
                symbol_list = []
                for symbol in symbol_stats.index:
                    symbol_list.append({
                        'symbol': symbol,
                        'trades': int(symbol_stats.loc[symbol, ('profit', 'count')]),
                        'total_profit': symbol_stats.loc[symbol, ('profit', 'sum')],
                        'avg_profit': symbol_stats.loc[symbol, ('profit', 'mean')]
                    })
                    
                # 按总盈利排序
                symbol_list.sort(key=lambda x: x['total_profit'], reverse=True)
                analysis['symbol_stats'] = symbol_list[:10]  # 取前10名
                
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing trades: {e}")
            return {}
            
    def _analyze_positions(self, positions_df: pd.DataFrame) -> Dict[str, Any]:
        """分析持仓"""
        try:
            analysis = {}
            
            if 'symbol' in positions_df.columns and 'quantity' in positions_df.columns:
                # 持仓股票数量
                unique_symbols = positions_df['symbol'].nunique()
                analysis['unique_symbols'] = unique_symbols
                
                # 平均持仓时间（如果有日期信息）
                if 'date' in positions_df.columns:
                    position_days = positions_df.groupby('symbol')['date'].nunique()
                    analysis['avg_holding_days'] = f"{position_days.mean():.1f}"
                    
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing positions: {e}")
            return {}
            
    async def _generate_charts(self, result: BacktestResult, output_path: str) -> Dict[str, str]:
        """
        生成图表
        
        Args:
            result: 回测结果
            output_path: 输出路径
            
        Returns:
            Dict[str, str]: 图表Base64编码字典
        """
        charts = {}
        
        try:
            # 净值曲线图
            if result.performance_file and os.path.exists(result.performance_file):
                nav_chart = await self._create_nav_chart(result.performance_file)
                if nav_chart:
                    charts['nav_chart'] = nav_chart
                    
                # 回撤图
                drawdown_chart = await self._create_drawdown_chart(result.performance_file)
                if drawdown_chart:
                    charts['drawdown_chart'] = drawdown_chart
                    
            # 月度收益热力图
            if result.performance_file and os.path.exists(result.performance_file):
                monthly_heatmap = await self._create_monthly_heatmap(result.performance_file)
                if monthly_heatmap:
                    charts['monthly_heatmap'] = monthly_heatmap
                    
            # 交易分布图
            if result.trades_file and os.path.exists(result.trades_file):
                trade_distribution = await self._create_trade_distribution_chart(result.trades_file)
                if trade_distribution:
                    charts['trade_distribution'] = trade_distribution
                    
        except Exception as e:
            logger.error(f"Error generating charts: {e}")
            
        return charts
        
    async def _create_nav_chart(self, performance_file: str) -> Optional[str]:
        """创建净值曲线图"""
        try:
            df = pd.read_csv(performance_file)
            if df.empty or 'date' not in df.columns or 'total_asset' not in df.columns:
                return None
                
            df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
            
            plt.figure(figsize=(12, 6))
            plt.plot(df['date'], df['total_asset'], linewidth=2, color='#1f77b4')
            plt.title('净值曲线', fontsize=16, fontweight='bold')
            plt.xlabel('日期', fontsize=12)
            plt.ylabel('净值', fontsize=12)
            plt.grid(True, alpha=0.3)
            
            # 格式化x轴
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            logger.error(f"Error creating nav chart: {e}")
            return None
            
    async def _create_drawdown_chart(self, performance_file: str) -> Optional[str]:
        """创建回撤图"""
        try:
            df = pd.read_csv(performance_file)
            if df.empty or 'date' not in df.columns or 'total_asset' not in df.columns:
                return None
                
            df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
            
            # 计算回撤
            cumulative = df['total_asset']
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            
            plt.figure(figsize=(12, 6))
            plt.fill_between(df['date'], drawdown, 0, alpha=0.3, color='red')
            plt.plot(df['date'], drawdown, linewidth=1, color='red')
            plt.title('回撤曲线', fontsize=16, fontweight='bold')
            plt.xlabel('日期', fontsize=12)
            plt.ylabel('回撤比例', fontsize=12)
            plt.grid(True, alpha=0.3)
            
            # 格式化y轴为百分比
            plt.gca().yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.1%}'))
            
            # 格式化x轴
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            plt.gca().xaxis.set_major_locator(mdates.MonthLocator(interval=1))
            plt.xticks(rotation=45)
            
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            logger.error(f"Error creating drawdown chart: {e}")
            return None
            
    async def _create_monthly_heatmap(self, performance_file: str) -> Optional[str]:
        """创建月度收益热力图"""
        try:
            df = pd.read_csv(performance_file)
            if df.empty or 'date' not in df.columns or 'total_asset' not in df.columns:
                return None
                
            df['date'] = pd.to_datetime(df['date'], format='%Y%m%d')
            df.set_index('date', inplace=True)
            
            # 计算月度收益
            monthly_data = df['total_asset'].resample('M').last()
            monthly_returns = monthly_data.pct_change().dropna()
            
            if monthly_returns.empty:
                return None
                
            # 创建年月矩阵
            monthly_returns.index = pd.to_datetime(monthly_returns.index)
            pivot_data = monthly_returns.groupby([monthly_returns.index.year, monthly_returns.index.month]).first().unstack()
            
            plt.figure(figsize=(12, 8))
            sns.heatmap(pivot_data, annot=True, fmt='.2%', cmap='RdYlGn', center=0,
                       cbar_kws={'label': '月度收益率'})
            plt.title('月度收益热力图', fontsize=16, fontweight='bold')
            plt.xlabel('月份', fontsize=12)
            plt.ylabel('年份', fontsize=12)
            
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            logger.error(f"Error creating monthly heatmap: {e}")
            return None
            
    async def _create_trade_distribution_chart(self, trades_file: str) -> Optional[str]:
        """创建交易分布图"""
        try:
            df = pd.read_csv(trades_file)
            if df.empty or 'profit' not in df.columns:
                return None
                
            plt.figure(figsize=(12, 6))
            
            # 创建直方图
            plt.hist(df['profit'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
            plt.axvline(x=0, color='red', linestyle='--', linewidth=2, label='盈亏平衡线')
            
            plt.title('交易盈亏分布', fontsize=16, fontweight='bold')
            plt.xlabel('单笔盈亏', fontsize=12)
            plt.ylabel('交易次数', fontsize=12)
            plt.legend()
            plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            
            # 转换为Base64
            buffer = BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            chart_base64 = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
            return chart_base64
            
        except Exception as e:
            logger.error(f"Error creating trade distribution chart: {e}")
            return None
            
    def _render_html_template(self, report_data: Dict[str, Any]) -> str:
        """渲染HTML模板"""
        try:
            # 如果模板文件不存在，使用默认模板
            if not os.path.exists(self.template_path):
                template_content = self._get_default_template()
            else:
                with open(self.template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                    
            template = Template(template_content)
            return template.render(**report_data)
            
        except Exception as e:
            logger.error(f"Error rendering HTML template: {e}")
            return self._get_simple_template().render(**report_data)
            
    def _get_default_template(self) -> str:
        """获取默认HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>回测报告 - {{ basic_info.task_name }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .section { margin-bottom: 30px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .metric-card { background: #f5f5f5; padding: 15px; border-radius: 5px; }
        .chart { text-align: center; margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>回测报告</h1>
        <h2>{{ basic_info.task_name }}</h2>
        <p>策略: {{ basic_info.strategy_name }} | 生成时间: {{ basic_info.generate_time }}</p>
    </div>
    
    <div class="section">
        <h3>基本信息</h3>
        <div class="metrics-grid">
            <div class="metric-card">
                <strong>回测期间</strong><br>
                {{ basic_info.start_date }} - {{ basic_info.end_date }}
            </div>
            <div class="metric-card">
                <strong>初始资金</strong><br>
                {{ basic_info.initial_capital }}
            </div>
            <div class="metric-card">
                <strong>最终资金</strong><br>
                {{ basic_info.final_capital }}
            </div>
        </div>
    </div>
    
    <div class="section">
        <h3>收益指标</h3>
        <div class="metrics-grid">
            {% for key, value in performance_metrics.items() %}
            <div class="metric-card">
                <strong>{{ key }}</strong><br>
                {{ value }}
            </div>
            {% endfor %}
        </div>
    </div>
    
    <div class="section">
        <h3>交易统计</h3>
        <div class="metrics-grid">
            {% for key, value in trading_stats.items() %}
            <div class="metric-card">
                <strong>{{ key }}</strong><br>
                {{ value }}
            </div>
            {% endfor %}
        </div>
    </div>
    
    {% if charts %}
    <div class="section">
        <h3>图表分析</h3>
        {% if charts.nav_chart %}
        <div class="chart">
            <img src="data:image/png;base64,{{ charts.nav_chart }}" alt="净值曲线" style="max-width: 100%;">
        </div>
        {% endif %}
        
        {% if charts.drawdown_chart %}
        <div class="chart">
            <img src="data:image/png;base64,{{ charts.drawdown_chart }}" alt="回撤曲线" style="max-width: 100%;">
        </div>
        {% endif %}
    </div>
    {% endif %}
</body>
</html>
        """
        
    def _get_simple_template(self) -> Template:
        """获取简单模板"""
        return Template(self._get_default_template())
