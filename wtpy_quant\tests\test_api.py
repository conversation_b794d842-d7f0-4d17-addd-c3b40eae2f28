"""
API服务测试
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch
from fastapi.testclient import TestClient

from wtpy_quant.api.main import app


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


@pytest.fixture
def mock_app_state():
    """模拟应用状态"""
    with patch('wtpy_quant.api.main.app_state') as mock_state:
        mock_state['initialized'] = True
        mock_state['config'] = MagicMock()
        mock_state['engine'] = MagicMock()
        mock_state['backtest_engine'] = MagicMock()
        mock_state['concept_manager'] = MagicMock()
        yield mock_state


class TestMainAPI:
    """主API测试"""
    
    def test_root_endpoint(self, client):
        """测试根路径"""
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "status" in data
        
    def test_health_check_not_initialized(self, client):
        """测试健康检查 - 未初始化"""
        response = client.get("/health")
        assert response.status_code == 503
        
    def test_health_check_initialized(self, client, mock_app_state):
        """测试健康检查 - 已初始化"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "components" in data
        
    def test_system_info(self, client, mock_app_state):
        """测试系统信息"""
        mock_app_state['config'].engine.mode = "live"
        mock_app_state['config'].data.path = "/data"
        mock_app_state['config'].api.host = "0.0.0.0"
        mock_app_state['config'].api.port = 8000
        mock_app_state['engine'].is_running.return_value = True
        
        response = client.get("/info")
        assert response.status_code == 200
        data = response.json()
        assert "system" in data
        assert "config" in data
        assert "status" in data


class TestStrategyAPI:
    """策略API测试"""
    
    def test_list_strategies(self, client, mock_app_state):
        """测试策略列表"""
        # Mock策略管理器
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        
        mock_strategy_info = MagicMock()
        mock_strategy_info.name = "TestStrategy"
        mock_strategy_info.strategy_type = "ConceptStrategy"
        mock_strategy_info.status.value = "active"
        mock_strategy_info.description = "Test description"
        mock_strategy_info.parameters = {"param1": 10}
        mock_strategy_info.create_time = "2023-01-01T00:00:00"
        mock_strategy_info.update_time = "2023-01-01T00:00:00"
        
        mock_strategy_manager.list_strategies.return_value = {
            "strategy_001": mock_strategy_info
        }
        
        response = client.get("/api/v1/strategy/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["strategy_id"] == "strategy_001"
        assert data[0]["name"] == "TestStrategy"
        
    def test_create_strategy(self, client, mock_app_state):
        """测试创建策略"""
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        
        # Mock注册策略返回
        mock_strategy_manager.register_strategy.return_value = "new_strategy_id"
        
        mock_strategy_info = MagicMock()
        mock_strategy_info.name = "NewStrategy"
        mock_strategy_info.strategy_type = "ConceptStrategy"
        mock_strategy_info.status.value = "inactive"
        mock_strategy_info.description = "New strategy"
        mock_strategy_info.parameters = {"max_positions": 5}
        mock_strategy_info.create_time = "2023-01-01T00:00:00"
        mock_strategy_info.update_time = "2023-01-01T00:00:00"
        
        mock_strategy_manager.get_strategy_info.return_value = mock_strategy_info
        
        request_data = {
            "name": "NewStrategy",
            "type": "ConceptStrategy",
            "description": "New strategy",
            "parameters": {"max_positions": 5}
        }
        
        response = client.post("/api/v1/strategy/", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "NewStrategy"
        assert data["type"] == "ConceptStrategy"
        
    def test_get_strategy(self, client, mock_app_state):
        """测试获取策略详情"""
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        
        mock_strategy_info = MagicMock()
        mock_strategy_info.name = "TestStrategy"
        mock_strategy_info.strategy_type = "ConceptStrategy"
        mock_strategy_info.status.value = "active"
        mock_strategy_info.description = "Test description"
        mock_strategy_info.parameters = {"param1": 10}
        mock_strategy_info.create_time = "2023-01-01T00:00:00"
        mock_strategy_info.update_time = "2023-01-01T00:00:00"
        
        mock_strategy_manager.get_strategy_info.return_value = mock_strategy_info
        
        response = client.get("/api/v1/strategy/strategy_001")
        assert response.status_code == 200
        data = response.json()
        assert data["strategy_id"] == "strategy_001"
        assert data["name"] == "TestStrategy"
        
    def test_get_strategy_not_found(self, client, mock_app_state):
        """测试获取不存在的策略"""
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        mock_strategy_manager.get_strategy_info.return_value = None
        
        response = client.get("/api/v1/strategy/nonexistent")
        assert response.status_code == 404
        
    def test_control_strategy(self, client, mock_app_state):
        """测试策略控制"""
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        
        mock_strategy_info = MagicMock()
        mock_strategy_info.status.value = "inactive"
        mock_strategy_manager.get_strategy_info.return_value = mock_strategy_info
        mock_strategy_manager.start_strategy.return_value = True
        
        request_data = {"action": "start"}
        response = client.post("/api/v1/strategy/strategy_001/control", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert "started" in data["message"]
        
    def test_get_supported_strategy_types(self, client):
        """测试获取支持的策略类型"""
        response = client.get("/api/v1/strategy/types/supported")
        assert response.status_code == 200
        data = response.json()
        assert "supported_types" in data
        assert len(data["supported_types"]) > 0


class TestBacktestAPI:
    """回测API测试"""
    
    def test_list_backtests(self, client, mock_app_state):
        """测试回测列表"""
        mock_backtest_engine = MagicMock()
        mock_app_state['backtest_engine'] = mock_backtest_engine
        
        mock_task = MagicMock()
        mock_task.name = "TestBacktest"
        mock_task.strategy_type = "ConceptStrategy"
        mock_task.status = "completed"
        mock_task.start_date.isoformat.return_value = "2023-01-01"
        mock_task.end_date.isoformat.return_value = "2023-12-31"
        mock_task.initial_capital = 100000.0
        mock_task.create_time.isoformat.return_value = "2023-01-01T00:00:00"
        mock_task.start_time = None
        mock_task.end_time = None
        mock_task.progress = 100.0
        mock_task.error_message = None
        
        mock_backtest_engine.list_tasks.return_value = {"task_001": mock_task}
        
        response = client.get("/api/v1/backtest/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["task_id"] == "task_001"
        assert data[0]["name"] == "TestBacktest"
        
    def test_create_backtest(self, client, mock_app_state):
        """测试创建回测"""
        mock_backtest_engine = MagicMock()
        mock_app_state['backtest_engine'] = mock_backtest_engine
        
        # Mock异步方法
        async def mock_submit_task(task):
            return "new_task_id"
        mock_backtest_engine.submit_task = mock_submit_task
        
        request_data = {
            "name": "NewBacktest",
            "strategy_type": "ConceptStrategy",
            "strategy_params": {"max_positions": 10},
            "start_date": "2023-01-01",
            "end_date": "2023-12-31",
            "initial_capital": 100000.0,
            "symbols": ["000001.SZ", "000002.SZ"],
            "benchmark": "000300.SH"
        }
        
        response = client.post("/api/v1/backtest/", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert data["task_id"] == "new_task_id"
        assert data["name"] == "NewBacktest"


class TestDataAPI:
    """数据API测试"""
    
    def test_get_available_symbols(self, client):
        """测试获取可用股票代码"""
        response = client.get("/api/v1/data/market/symbols")
        assert response.status_code == 200
        data = response.json()
        assert "symbols" in data
        assert len(data["symbols"]) > 0
        
    def test_get_market_bars(self, client):
        """测试获取K线数据"""
        request_data = {
            "symbols": ["000001.SZ", "000002.SZ"],
            "start_date": "2023-01-01",
            "end_date": "2023-01-31",
            "frequency": "1d"
        }
        
        response = client.post("/api/v1/data/market/bars", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "000001.SZ" in data["data"]
        assert "000002.SZ" in data["data"]
        
    def test_calculate_indicator(self, client):
        """测试计算技术指标"""
        request_data = {
            "symbol": "000001.SZ",
            "indicator": "sma",
            "period": 20,
            "start_date": "2023-01-01",
            "end_date": "2023-01-31"
        }
        
        response = client.post("/api/v1/data/indicators/calculate", json=request_data)
        assert response.status_code == 200
        data = response.json()
        assert data["symbol"] == "000001.SZ"
        assert data["indicator"] == "sma"
        assert "values" in data
        
    def test_get_supported_indicators(self, client):
        """测试获取支持的指标"""
        response = client.get("/api/v1/data/indicators/supported")
        assert response.status_code == 200
        data = response.json()
        assert "indicators" in data
        assert len(data["indicators"]) > 0


class TestMonitoringAPI:
    """监控API测试"""
    
    def test_get_system_status(self, client):
        """测试获取系统状态"""
        response = client.get("/api/v1/monitoring/system/status")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "uptime" in data
        assert "cpu_usage" in data
        assert "memory_usage" in data
        
    def test_get_strategy_monitors(self, client, mock_app_state):
        """测试获取策略监控"""
        mock_strategy_manager = MagicMock()
        mock_app_state['engine'].strategy_manager = mock_strategy_manager
        
        mock_strategy_info = MagicMock()
        mock_strategy_info.name = "TestStrategy"
        mock_strategy_info.status.value = "active"
        
        mock_strategy = MagicMock()
        mock_strategy.get_stats.return_value = {
            'total_pnl': 1250.0,
            'orders_placed': 10,
            'error_count': 0
        }
        mock_strategy.get_positions.return_value = {"000001.SZ": 1000}
        
        mock_strategy_manager.list_strategies.return_value = {"strategy_001": mock_strategy_info}
        mock_strategy_manager.get_strategy.return_value = mock_strategy
        
        response = client.get("/api/v1/monitoring/strategies/")
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["strategy_id"] == "strategy_001"
        assert data[0]["pnl"] == 1250.0
        
    def test_get_risk_metrics(self, client):
        """测试获取风险指标"""
        response = client.get("/api/v1/monitoring/risk/metrics")
        assert response.status_code == 200
        data = response.json()
        assert "total_exposure" in data
        assert "max_drawdown" in data
        assert "var_95" in data
        
    def test_get_portfolio_summary(self, client):
        """测试获取投资组合摘要"""
        response = client.get("/api/v1/monitoring/portfolio/summary")
        assert response.status_code == 200
        data = response.json()
        assert "total_value" in data
        assert "cash" in data
        assert "positions_value" in data
        assert "top_positions" in data


if __name__ == "__main__":
    pytest.main([__file__])
