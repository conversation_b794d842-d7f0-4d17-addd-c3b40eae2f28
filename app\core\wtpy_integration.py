"""
Thin facades around wtpy/wondertrader engines for backtest and live trading.
These keep the rest of the app decoupled from specific wtpy API details.

Note: Real wiring requires wtpy to be installed. We guard imports and provide
clear error messages if missing. Adjust the calls below to your wtpy version.
"""
from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Optional


@dataclass
class RunResult:
    ok: bool
    message: str
    details: Optional[dict] = None


class WtpyUnavailableError(RuntimeError):
    pass


def _ensure_wtpy() -> Any:
    try:
        import wtpy  # type: ignore
        return wtpy
    except Exception as e:  # pragma: no cover
        raise WtpyUnavailableError(
            "wtpy is not installed or failed to import. Please install wondertrader/wtpy per docs.") from e


class BacktestFacade:
    """Backtest runner facade. Delegates to wtpy's C++-backed engine when available."""

    def run(self, strategy_py: str, config_path: str | Path, out_dir: str | Path | None = None) -> RunResult:
        wtpy = _ensure_wtpy()
        try:
            from wtpy import WtBtEngine  # type: ignore
        except Exception as e:
            return RunResult(ok=False, message=f"Failed to import wtpy backtest engine: {e}")

        try:
            outp = Path(out_dir) if out_dir else Path("runs/backtest")
            outp.mkdir(parents=True, exist_ok=True)
            bt = WtBtEngine()
            # Initialize with folder & config
            if hasattr(bt, "init"):
                try:
                    bt.init(folder=str(outp), cfgfile=str(config_path))  # type: ignore[arg-type]
                except TypeError:
                    bt.init(str(outp), str(config_path))  # type: ignore[misc]

            # Try to load/register strategy if supported by this wtpy version
            # Many demos use bt.set_external_cta/sel/evt/… Or strategies are defined in config.
            try:
                from app.core.loader import load
                obj = load(strategy_py)
                # Heuristic registration attempts; ignore if not applicable.
                if hasattr(bt, "set_external_cta"):
                    bt.set_external_cta(obj)  # type: ignore[attr-defined]
                elif hasattr(bt, "set_external_evt"):
                    bt.set_external_evt(obj)  # type: ignore[attr-defined]
                # else: assume config will reference the strategy script
            except Exception:
                pass

            # Run backtest
            # Broadcast start
            try:
                from app.core.events import manager
                import time
                awaitable = getattr(manager, "broadcast", None)
                if callable(awaitable):
                    import asyncio
                    asyncio.create_task(manager.broadcast({"type": "bt_start", "config": str(config_path)}))
            except Exception:
                pass

            if hasattr(bt, "run_backtest"):
                bt.run_backtest()
            elif hasattr(bt, "runBt"):
                bt.runBt()  # type: ignore[attr-defined]
            else:
                return RunResult(ok=False, message="Unknown wtpy backtest run method. Please adjust facade.")

            try:
                from app.core.results import parse_equity
                series = parse_equity(outp)
            except Exception:
                series = []
            try:
                import asyncio
                from app.core.events import manager
                asyncio.create_task(manager.broadcast({"type": "bt_done", "out_dir": str(outp), "equity": series}))
            except Exception:
                pass

            return RunResult(ok=True, message="Backtest finished", details={"out_dir": str(outp)})
        except Exception as e:
            return RunResult(ok=False, message=f"Backtest failed: {e}")


class LiveFacade:
    """Live trading runner facade."""

    def run(self, config_path: str | Path) -> RunResult:
        try:
            from wtpy import WtEngine  # type: ignore
        except Exception as e:
            return RunResult(ok=False, message=f"Failed to import wtpy live engine: {e}")
        try:
            eng = WtEngine()
            # Initialize engine with a config file; the exact API may vary by version.
            if hasattr(eng, "init"):
                try:
                    eng.init(cfgfile=str(config_path))  # type: ignore[arg-type]
                except TypeError:
                    eng.init(str(config_path))  # type: ignore[misc]
            # Start run loop
            try:
                import asyncio
                from app.core.events import manager
                asyncio.create_task(manager.broadcast({"type": "live_start", "config": str(config_path)}))
            except Exception:
                pass

            if hasattr(eng, "run"):
                eng.run()
            elif hasattr(eng, "start"):
                eng.start()  # type: ignore[attr-defined]
            else:
                return RunResult(ok=False, message="Unknown wtpy live run method. Please adjust facade.")
            return RunResult(ok=True, message="Live engine started")
        except Exception as e:
            return RunResult(ok=False, message=f"Live run failed: {e}")

