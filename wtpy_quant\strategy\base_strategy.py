"""
策略基类 - wtpy策略框架集成

提供策略开发的基础框架，包括：
- wtpy策略基类继承
- 数据访问接口
- 交易接口封装
- 工具函数集成
"""

import logging
from typing import Any, Dict, List, Optional, Callable
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from datetime import datetime, time

logger = logging.getLogger(__name__)


class BaseStrategy(ABC):
    """策略基类"""
    
    def __init__(self, name: str, **kwargs):
        """
        初始化策略
        
        Args:
            name: 策略名称
            **kwargs: 策略参数
        """
        self.name = name
        self.params = kwargs
        
        # 策略状态
        self._initialized = False
        self._running = False
        
        # 数据缓存
        self._tick_cache: Dict[str, Any] = {}
        self._bar_cache: Dict[str, pd.DataFrame] = {}
        
        # 持仓信息
        self._positions: Dict[str, float] = {}
        
        # 统计信息
        self._stats = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'orders_placed': 0,
            'trades_executed': 0
        }
        
        # 回调函数
        self._signal_callbacks: List[Callable] = []
        
        logger.info(f"Strategy {name} initialized with params: {kwargs}")
        
    @abstractmethod
    def on_init(self, context: Any) -> None:
        """
        策略初始化
        
        Args:
            context: wtpy上下文对象
        """
        pass
        
    @abstractmethod
    def on_tick(self, context: Any, stdCode: str, newTick: Any) -> None:
        """
        Tick数据回调
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            newTick: Tick数据
        """
        pass
        
    @abstractmethod
    def on_bar(self, context: Any, stdCode: str, period: str, newBar: Any) -> None:
        """
        Bar数据回调
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            period: 周期
            newBar: Bar数据
        """
        pass
        
    def on_order(self, context: Any, localid: int, stdCode: str, isBuy: bool, 
                 totalQty: float, leftQty: float, price: float, isCanceled: bool) -> None:
        """
        订单回调
        
        Args:
            context: wtpy上下文对象
            localid: 本地订单ID
            stdCode: 标准代码
            isBuy: 是否买入
            totalQty: 总数量
            leftQty: 剩余数量
            price: 价格
            isCanceled: 是否已撤销
        """
        try:
            side = "买入" if isBuy else "卖出"
            status = "已撤销" if isCanceled else "部分成交" if leftQty > 0 else "全部成交"
            
            logger.info(f"订单回调: {stdCode} {side} {totalQty}@{price} {status}")
            
        except Exception as e:
            logger.error(f"Error in on_order: {e}")
            
    def on_trade(self, context: Any, localid: int, stdCode: str, isBuy: bool, 
                 qty: float, price: float) -> None:
        """
        成交回调
        
        Args:
            context: wtpy上下文对象
            localid: 本地订单ID
            stdCode: 标准代码
            isBuy: 是否买入
            qty: 成交数量
            price: 成交价格
        """
        try:
            side = "买入" if isBuy else "卖出"
            logger.info(f"成交回调: {stdCode} {side} {qty}@{price}")
            
            # 更新持仓
            current_pos = self._positions.get(stdCode, 0)
            if isBuy:
                self._positions[stdCode] = current_pos + qty
            else:
                self._positions[stdCode] = current_pos - qty
                
            self._stats['trades_executed'] += 1
            
        except Exception as e:
            logger.error(f"Error in on_trade: {e}")
            
    def on_timer(self, context: Any, id: int, times: int) -> None:
        """
        定时器回调
        
        Args:
            context: wtpy上下文对象
            id: 定时器ID
            times: 触发次数
        """
        try:
            logger.debug(f"定时器回调: ID={id}, 次数={times}")
            
        except Exception as e:
            logger.error(f"Error in on_timer: {e}")
            
    # 数据访问接口
    def get_bars(self, context: Any, stdCode: str, period: str, count: int) -> Optional[pd.DataFrame]:
        """
        获取K线数据
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            period: 周期
            count: 数量
            
        Returns:
            Optional[pd.DataFrame]: K线数据
        """
        try:
            # 从wtpy获取数据
            bars = context.stra_get_bars(stdCode, period, count)
            if bars is None or len(bars) == 0:
                return None
                
            # 转换为DataFrame
            df = pd.DataFrame(bars)
            if not df.empty:
                # 标准化列名
                if 'bartime' in df.columns:
                    df['datetime'] = pd.to_datetime(df['bartime'], format='%Y%m%d%H%M%S')
                    df.set_index('datetime', inplace=True)
                    
                # 缓存数据
                cache_key = f"{stdCode}_{period}"
                self._bar_cache[cache_key] = df
                
            return df
            
        except Exception as e:
            logger.error(f"Error getting bars for {stdCode}: {e}")
            return None
            
    def get_ticks(self, context: Any, stdCode: str, count: int) -> Optional[pd.DataFrame]:
        """
        获取Tick数据
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            count: 数量
            
        Returns:
            Optional[pd.DataFrame]: Tick数据
        """
        try:
            # 从wtpy获取数据
            ticks = context.stra_get_ticks(stdCode, count)
            if ticks is None or len(ticks) == 0:
                return None
                
            # 转换为DataFrame
            df = pd.DataFrame(ticks)
            if not df.empty:
                # 标准化列名
                if 'actiontime' in df.columns:
                    df['datetime'] = pd.to_datetime(df['actiontime'], unit='s')
                    df.set_index('datetime', inplace=True)
                    
            return df
            
        except Exception as e:
            logger.error(f"Error getting ticks for {stdCode}: {e}")
            return None
            
    def get_position(self, context: Any, stdCode: str) -> float:
        """
        获取持仓
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            
        Returns:
            float: 持仓数量
        """
        try:
            return context.stra_get_position(stdCode)
        except Exception as e:
            logger.error(f"Error getting position for {stdCode}: {e}")
            return 0.0
            
    def get_price(self, context: Any, stdCode: str) -> float:
        """
        获取最新价格
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            
        Returns:
            float: 最新价格
        """
        try:
            return context.stra_get_price(stdCode)
        except Exception as e:
            logger.error(f"Error getting price for {stdCode}: {e}")
            return 0.0
            
    # 交易接口
    def buy(self, context: Any, stdCode: str, qty: float, price: float = 0.0) -> bool:
        """
        买入
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            qty: 数量
            price: 价格（0为市价）
            
        Returns:
            bool: 是否成功
        """
        try:
            if price > 0:
                # 限价单
                context.stra_enter_long(stdCode, qty, "buy_signal", price)
            else:
                # 市价单
                context.stra_enter_long(stdCode, qty, "buy_signal")
                
            self._stats['orders_placed'] += 1
            self._stats['buy_signals'] += 1
            self._stats['total_signals'] += 1
            
            logger.info(f"买入信号: {stdCode} {qty}@{price if price > 0 else '市价'}")
            
            # 调用信号回调
            self._call_signal_callbacks("buy", stdCode, qty, price)
            
            return True
            
        except Exception as e:
            logger.error(f"Error buying {stdCode}: {e}")
            return False
            
    def sell(self, context: Any, stdCode: str, qty: float, price: float = 0.0) -> bool:
        """
        卖出
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            qty: 数量
            price: 价格（0为市价）
            
        Returns:
            bool: 是否成功
        """
        try:
            if price > 0:
                # 限价单
                context.stra_exit_long(stdCode, qty, "sell_signal", price)
            else:
                # 市价单
                context.stra_exit_long(stdCode, qty, "sell_signal")
                
            self._stats['orders_placed'] += 1
            self._stats['sell_signals'] += 1
            self._stats['total_signals'] += 1
            
            logger.info(f"卖出信号: {stdCode} {qty}@{price if price > 0 else '市价'}")
            
            # 调用信号回调
            self._call_signal_callbacks("sell", stdCode, qty, price)
            
            return True
            
        except Exception as e:
            logger.error(f"Error selling {stdCode}: {e}")
            return False
            
    def set_position(self, context: Any, stdCode: str, qty: float, price: float = 0.0) -> bool:
        """
        设置目标持仓
        
        Args:
            context: wtpy上下文对象
            stdCode: 标准代码
            qty: 目标数量
            price: 价格（0为市价）
            
        Returns:
            bool: 是否成功
        """
        try:
            current_pos = self.get_position(context, stdCode)
            diff = qty - current_pos
            
            if abs(diff) < 1:  # 忽略小于1股的差异
                return True
                
            if diff > 0:
                # 需要买入
                return self.buy(context, stdCode, diff, price)
            else:
                # 需要卖出
                return self.sell(context, stdCode, abs(diff), price)
                
        except Exception as e:
            logger.error(f"Error setting position for {stdCode}: {e}")
            return False
            
    # 工具函数
    def log_info(self, message: str) -> None:
        """记录信息日志"""
        logger.info(f"[{self.name}] {message}")
        
    def log_warning(self, message: str) -> None:
        """记录警告日志"""
        logger.warning(f"[{self.name}] {message}")
        
    def log_error(self, message: str) -> None:
        """记录错误日志"""
        logger.error(f"[{self.name}] {message}")
        
    def is_trading_time(self) -> bool:
        """判断是否在交易时间"""
        now = datetime.now().time()
        
        # A股交易时间
        morning_start = time(9, 30)
        morning_end = time(11, 30)
        afternoon_start = time(13, 0)
        afternoon_end = time(15, 0)
        
        return (morning_start <= now <= morning_end) or (afternoon_start <= now <= afternoon_end)
        
    def register_signal_callback(self, callback: Callable) -> None:
        """注册信号回调"""
        self._signal_callbacks.append(callback)
        
    def _call_signal_callbacks(self, signal_type: str, stdCode: str, qty: float, price: float) -> None:
        """调用信号回调"""
        for callback in self._signal_callbacks:
            try:
                callback(signal_type, stdCode, qty, price)
            except Exception as e:
                logger.error(f"Error in signal callback: {e}")
                
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
        
    def get_positions(self) -> Dict[str, float]:
        """获取所有持仓"""
        return self._positions.copy()
        
    def get_param(self, key: str, default: Any = None) -> Any:
        """获取参数"""
        return self.params.get(key, default)
        
    def set_param(self, key: str, value: Any) -> None:
        """设置参数"""
        self.params[key] = value
