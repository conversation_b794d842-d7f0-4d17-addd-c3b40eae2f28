"""
回测引擎 - wtpy回测引擎集成

负责回测任务的管理和执行，包括：
- wtpy回测引擎配置
- 回测任务管理
- 结果分析与报告生成
- 参数优化支持
"""

import asyncio
import logging
import os
import json
import yaml
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, date
from pathlib import Path
import pandas as pd
import numpy as np

from ..core.config_manager import BacktestConfig

logger = logging.getLogger(__name__)


@dataclass
class BacktestTask:
    """回测任务"""
    task_id: str
    name: str
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    config: Dict[str, Any] = field(default_factory=dict)
    status: str = "pending"  # pending, running, completed, failed
    create_time: str = ""
    start_time: str = ""
    end_time: str = ""
    result_path: str = ""
    error_message: str = ""
    
    def __post_init__(self):
        if not self.create_time:
            self.create_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


@dataclass
class BacktestResult:
    """回测结果"""
    task_id: str
    strategy_name: str
    start_date: str
    end_date: str
    initial_capital: float
    final_capital: float
    
    # 收益指标
    total_return: float = 0.0
    annualized_return: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: float = 0.0
    calmar_ratio: float = 0.0
    
    # 交易统计
    total_trades: int = 0
    win_rate: float = 0.0
    profit_loss_ratio: float = 0.0
    
    # 详细数据路径
    trades_file: str = ""
    positions_file: str = ""
    performance_file: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'strategy_name': self.strategy_name,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'initial_capital': self.initial_capital,
            'final_capital': self.final_capital,
            'total_return': self.total_return,
            'annualized_return': self.annualized_return,
            'max_drawdown': self.max_drawdown,
            'sharpe_ratio': self.sharpe_ratio,
            'calmar_ratio': self.calmar_ratio,
            'total_trades': self.total_trades,
            'win_rate': self.win_rate,
            'profit_loss_ratio': self.profit_loss_ratio,
            'trades_file': self.trades_file,
            'positions_file': self.positions_file,
            'performance_file': self.performance_file
        }


class BacktestEngine:
    """回测引擎"""
    
    def __init__(self, config: BacktestConfig):
        """
        初始化回测引擎
        
        Args:
            config: 回测配置
        """
        self.config = config
        
        # 任务管理
        self._tasks: Dict[str, BacktestTask] = {}
        self._results: Dict[str, BacktestResult] = {}
        
        # 回调函数
        self._task_callbacks: List[Callable[[BacktestTask], None]] = []
        self._result_callbacks: List[Callable[[BacktestResult], None]] = []
        
        # 统计信息
        self._stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'running_tasks': 0
        }
        
        # 确保输出目录存在
        os.makedirs(self.config.output_path, exist_ok=True)
        
    async def create_task(self, 
                         name: str,
                         strategy_name: str,
                         start_date: str,
                         end_date: str,
                         initial_capital: float = 1000000.0,
                         config: Optional[Dict[str, Any]] = None) -> str:
        """
        创建回测任务
        
        Args:
            name: 任务名称
            strategy_name: 策略名称
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            initial_capital: 初始资金
            config: 额外配置
            
        Returns:
            str: 任务ID
        """
        try:
            task_id = f"bt_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self._tasks)}"
            
            task = BacktestTask(
                task_id=task_id,
                name=name,
                strategy_name=strategy_name,
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                config=config or {}
            )
            
            self._tasks[task_id] = task
            self._stats['total_tasks'] += 1
            
            logger.info(f"Created backtest task: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"Error creating backtest task: {e}")
            raise
            
    async def run_task(self, task_id: str) -> bool:
        """
        运行回测任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            bool: 是否成功启动
        """
        try:
            task = self._tasks.get(task_id)
            if not task:
                logger.error(f"Task not found: {task_id}")
                return False
                
            if task.status != "pending":
                logger.warning(f"Task {task_id} is not in pending status: {task.status}")
                return False
                
            # 更新任务状态
            task.status = "running"
            task.start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self._stats['running_tasks'] += 1
            
            # 调用任务回调
            for callback in self._task_callbacks:
                try:
                    callback(task)
                except Exception as e:
                    logger.error(f"Error in task callback: {e}")
                    
            # 异步执行回测
            asyncio.create_task(self._execute_backtest(task))
            
            logger.info(f"Started backtest task: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error running backtest task {task_id}: {e}")
            return False
            
    async def _execute_backtest(self, task: BacktestTask) -> None:
        """
        执行回测
        
        Args:
            task: 回测任务
        """
        try:
            logger.info(f"Executing backtest task: {task.task_id}")
            
            # 创建任务输出目录
            task_output_dir = os.path.join(self.config.output_path, task.task_id)
            os.makedirs(task_output_dir, exist_ok=True)
            task.result_path = task_output_dir
            
            # 生成wtpy回测配置
            bt_config = self._generate_backtest_config(task, task_output_dir)
            config_file = os.path.join(task_output_dir, "config.yaml")
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(bt_config, f, default_flow_style=False, allow_unicode=True)
                
            # 执行wtpy回测
            success = await self._run_wtpy_backtest(config_file, task_output_dir)
            
            if success:
                # 分析回测结果
                result = await self._analyze_backtest_result(task, task_output_dir)
                if result:
                    self._results[task.task_id] = result
                    
                    # 调用结果回调
                    for callback in self._result_callbacks:
                        try:
                            callback(result)
                        except Exception as e:
                            logger.error(f"Error in result callback: {e}")
                            
                    task.status = "completed"
                    self._stats['completed_tasks'] += 1
                    logger.info(f"Backtest task completed: {task.task_id}")
                else:
                    task.status = "failed"
                    task.error_message = "Failed to analyze backtest result"
                    self._stats['failed_tasks'] += 1
            else:
                task.status = "failed"
                task.error_message = "wtpy backtest execution failed"
                self._stats['failed_tasks'] += 1
                
        except Exception as e:
            logger.error(f"Error executing backtest task {task.task_id}: {e}")
            task.status = "failed"
            task.error_message = str(e)
            self._stats['failed_tasks'] += 1
            
        finally:
            task.end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self._stats['running_tasks'] = max(0, self._stats['running_tasks'] - 1)
            
    def _generate_backtest_config(self, task: BacktestTask, output_dir: str) -> Dict[str, Any]:
        """
        生成wtpy回测配置
        
        Args:
            task: 回测任务
            output_dir: 输出目录
            
        Returns:
            Dict[str, Any]: wtpy回测配置
        """
        config = {
            'backtest': {
                'start_date': int(task.start_date),
                'end_date': int(task.end_date),
                'init_capital': task.initial_capital,
                'strategy': task.strategy_name,
                'output_dir': output_dir
            },
            'data': {
                'store': {
                    'path': self.config.data_path
                }
            },
            'fees': {
                'stock': {
                    'open': 0.0003,  # 开仓手续费
                    'close': 0.0003,  # 平仓手续费
                    'close_today': 0.0003  # 平今手续费
                }
            },
            'slippage': {
                'stock': 0.002  # 滑点
            }
        }
        
        # 合并用户配置
        if task.config:
            config.update(task.config)
            
        return config
        
    async def _run_wtpy_backtest(self, config_file: str, output_dir: str) -> bool:
        """
        运行wtpy回测
        
        Args:
            config_file: 配置文件路径
            output_dir: 输出目录
            
        Returns:
            bool: 是否成功
        """
        try:
            # 检查wtpy是否可用
            try:
                from wtpy import WtBtEngine
            except ImportError:
                logger.error("wtpy is not available for backtesting")
                return False
                
            # 创建回测引擎
            engine = WtBtEngine()
            
            # 加载配置
            engine.init(config_file)
            
            # 运行回测
            engine.run_backtest()
            
            # 释放资源
            engine.release()
            
            return True
            
        except Exception as e:
            logger.error(f"Error running wtpy backtest: {e}")
            return False
            
    async def _analyze_backtest_result(self, task: BacktestTask, output_dir: str) -> Optional[BacktestResult]:
        """
        分析回测结果
        
        Args:
            task: 回测任务
            output_dir: 输出目录
            
        Returns:
            Optional[BacktestResult]: 回测结果
        """
        try:
            # 查找结果文件
            trades_file = os.path.join(output_dir, "trades.csv")
            positions_file = os.path.join(output_dir, "positions.csv")
            performance_file = os.path.join(output_dir, "performance.csv")
            
            # 检查文件是否存在
            if not os.path.exists(performance_file):
                logger.error(f"Performance file not found: {performance_file}")
                return None
                
            # 读取业绩数据
            performance_df = pd.read_csv(performance_file)
            
            if performance_df.empty:
                logger.error("Performance data is empty")
                return None
                
            # 计算关键指标
            final_capital = performance_df['total_asset'].iloc[-1] if 'total_asset' in performance_df.columns else task.initial_capital
            total_return = (final_capital - task.initial_capital) / task.initial_capital
            
            # 计算年化收益率
            start_date = datetime.strptime(task.start_date, "%Y%m%d")
            end_date = datetime.strptime(task.end_date, "%Y%m%d")
            days = (end_date - start_date).days
            annualized_return = (1 + total_return) ** (365 / days) - 1 if days > 0 else 0
            
            # 计算最大回撤
            if 'total_asset' in performance_df.columns:
                cumulative = performance_df['total_asset']
                running_max = cumulative.expanding().max()
                drawdown = (cumulative - running_max) / running_max
                max_drawdown = abs(drawdown.min())
            else:
                max_drawdown = 0.0
                
            # 计算夏普比率
            if 'daily_return' in performance_df.columns:
                daily_returns = performance_df['daily_return'].dropna()
                if len(daily_returns) > 1:
                    sharpe_ratio = daily_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
                else:
                    sharpe_ratio = 0.0
            else:
                sharpe_ratio = 0.0
                
            # 计算卡尔玛比率
            calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0
            
            # 交易统计
            total_trades = 0
            win_rate = 0.0
            profit_loss_ratio = 0.0
            
            if os.path.exists(trades_file):
                trades_df = pd.read_csv(trades_file)
                if not trades_df.empty:
                    total_trades = len(trades_df)
                    
                    if 'profit' in trades_df.columns:
                        profitable_trades = trades_df[trades_df['profit'] > 0]
                        losing_trades = trades_df[trades_df['profit'] < 0]
                        
                        win_rate = len(profitable_trades) / total_trades if total_trades > 0 else 0
                        
                        if len(profitable_trades) > 0 and len(losing_trades) > 0:
                            avg_profit = profitable_trades['profit'].mean()
                            avg_loss = abs(losing_trades['profit'].mean())
                            profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0
                            
            # 创建结果对象
            result = BacktestResult(
                task_id=task.task_id,
                strategy_name=task.strategy_name,
                start_date=task.start_date,
                end_date=task.end_date,
                initial_capital=task.initial_capital,
                final_capital=final_capital,
                total_return=total_return,
                annualized_return=annualized_return,
                max_drawdown=max_drawdown,
                sharpe_ratio=sharpe_ratio,
                calmar_ratio=calmar_ratio,
                total_trades=total_trades,
                win_rate=win_rate,
                profit_loss_ratio=profit_loss_ratio,
                trades_file=trades_file if os.path.exists(trades_file) else "",
                positions_file=positions_file if os.path.exists(positions_file) else "",
                performance_file=performance_file
            )
            
            # 保存结果摘要
            result_summary_file = os.path.join(output_dir, "result_summary.json")
            with open(result_summary_file, 'w', encoding='utf-8') as f:
                json.dump(result.to_dict(), f, indent=2, ensure_ascii=False)
                
            return result
            
        except Exception as e:
            logger.error(f"Error analyzing backtest result: {e}")
            return None
            
    def get_task(self, task_id: str) -> Optional[BacktestTask]:
        """获取任务信息"""
        return self._tasks.get(task_id)
        
    def get_result(self, task_id: str) -> Optional[BacktestResult]:
        """获取回测结果"""
        return self._results.get(task_id)
        
    def list_tasks(self, status: Optional[str] = None) -> List[BacktestTask]:
        """列出任务"""
        tasks = list(self._tasks.values())
        if status:
            tasks = [t for t in tasks if t.status == status]
        return sorted(tasks, key=lambda x: x.create_time, reverse=True)
        
    def register_task_callback(self, callback: Callable[[BacktestTask], None]) -> None:
        """注册任务回调"""
        self._task_callbacks.append(callback)
        
    def register_result_callback(self, callback: Callable[[BacktestResult], None]) -> None:
        """注册结果回调"""
        self._result_callbacks.append(callback)
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
