"""
交易执行模块

提供完整的交易执行和风险控制功能，包括：
- 交易适配器 (BrokerAdapter)
- 订单路由器 (OrderRouter)
- 风险控制引擎 (RiskEngine)
- 投资组合管理器 (Portfolio)
"""

from .broker_adapter import (
    BrokerAdapter,
    Order, Trade, Position, Account,
    OrderSide, OrderType, OrderStatus
)

from .order_router import (
    OrderRouter,
    OrderRequest, RoutedOrder,
    RoutingStrategy, RouteRule, AccountInfo
)

from .risk_engine import (
    RiskEngine,
    RiskRule, RiskEvent, RiskMetrics,
    RiskLevel, RiskAction
)

from .portfolio import (
    Portfolio,
    PortfolioSummary, PositionAnalysis, PerformanceMetrics
)

__all__ = [
    # 交易适配器
    'BrokerAdapter',
    'Order', 'Trade', 'Position', 'Account',
    'OrderSide', 'OrderType', 'OrderStatus',

    # 订单路由器
    'OrderRouter',
    'OrderRequest', 'RoutedOrder',
    'RoutingStrategy', 'RouteRule', 'AccountInfo',

    # 风险控制引擎
    'RiskEngine',
    'RiskRule', 'RiskEvent', 'RiskMetrics',
    'RiskLevel', 'RiskAction',

    # 投资组合管理器
    'Portfolio',
    'PortfolioSummary', 'PositionAnalysis', 'PerformanceMetrics'
]
