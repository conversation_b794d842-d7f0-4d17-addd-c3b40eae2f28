async function api(path, options = {}) {
  const res = await fetch(path, {
    headers: { 'Content-Type': 'application/json' },
    ...options,
  });
  const text = await res.text();
  try { return JSON.parse(text); } catch { return text; }
}

function setText(id, value) {
  document.getElementById(id).textContent = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
}

// Health
document.getElementById('btnPing').addEventListener('click', async () => {
  const data = await api('/health/ping');
  setText('pingStatus', JSON.stringify(data));
});

// Backtest
const btChartEl = document.getElementById('btChart');
let btChart;
function ensureBtChart(){
  if (!btChart) { btChart = echarts.init(btChartEl); btChart.setOption({xAxis:{type:'category',data:[]},yAxis:{type:'value'},series:[{type:'line',data:[]}],title:{text:'回测净值(示意)'}}); }
  return btChart;
}

document.getElementById('btnRunBt').addEventListener('click', async () => {
  const body = {
    strategy: document.getElementById('btStrategy').value,
    start: '2024-01-01',
    end: '2024-06-30',
    config_path: document.getElementById('btConfig').value,
    out_dir: document.getElementById('btOut').value,
  };
  const data = await api('/bt/run', { method: 'POST', body: JSON.stringify(body) });
  setText('btResult', data);
  ensureBtChart();
});

// Live
document.getElementById('btnRunLive').addEventListener('click', async () => {
  const body = { config_path: document.getElementById('liveConfig').value };
  const data = await api('/live/run', { method: 'POST', body: JSON.stringify(body) });
  setText('liveResult', data);
});

// Strategies
document.getElementById('btnListStrategies').addEventListener('click', async () => {
  const data = await api('/strategies/');
  setText('stResult', data);
});

document.getElementById('btnAddStrategy').addEventListener('click', async () => {
  const body = {
    name: document.getElementById('stName').value,
    description: document.getElementById('stDesc').value,
    enabled: document.getElementById('stEnabled').checked,
  };
  const data = await api('/strategies/', { method: 'POST', body: JSON.stringify(body) });
  setText('stResult', data);
});

// Data
const symbolInput = document.getElementById('dqSymbol');

document.getElementById('btnTick').addEventListener('click', async () => {
  const data = await api(`/data/tick?symbol=${encodeURIComponent(symbolInput.value)}`);
  setText('dataResult', data);
});

document.getElementById('btnBar').addEventListener('click', async () => {
  const data = await api(`/data/bar?symbol=${encodeURIComponent(symbolInput.value)}&freq=1m`);
  setText('dataResult', data);
});

// WebSocket realtime
(function(){
  try {
    const wsProto = location.protocol === 'https:' ? 'wss' : 'ws';
    const ws = new WebSocket(`${wsProto}://${location.host}/ws`);
    ws.onmessage = (ev) => {
      let msg; try { msg = JSON.parse(ev.data); } catch { msg = ev.data; }
      if (msg && msg.type === 'bt_done') {
        ensureBtChart();
        if (Array.isArray(msg.equity) && msg.equity.length) {
          const xs = msg.equity.map(p => String(p[0]));
          const ys = msg.equity.map(p => Number(p[1]));
          btChart.setOption({xAxis:{data:xs},series:[{data:ys}]});
        }
      }
      if (msg && msg.type) {
        const prev = document.getElementById('btResult').textContent;
        document.getElementById('btResult').textContent = (prev + '\n' + JSON.stringify(msg));
      }
    }
  } catch (e) { console.warn('WS disabled', e); }
})();

