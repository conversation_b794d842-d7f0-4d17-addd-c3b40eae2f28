try:
    from fastapi import APIRouter
    from fastapi import WebSocket, WebSocketDisconnect
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    WebSocket = None  # type: ignore
    WebSocketDisconnect = Exception  # type: ignore

from app.core.events import manager

router = APIRouter() if APIRouter else None


if router:
    @router.websocket("/ws")
    async def ws_endpoint(websocket: WebSocket):
        await manager.connect(websocket)
        try:
            while True:
                # Optional: read pings or commands; currently just keep alive
                await websocket.receive_text()
        except WebSocketDisconnect:
            await manager.disconnect(websocket)
        except Exception:
            await manager.disconnect(websocket)

