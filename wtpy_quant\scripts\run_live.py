#!/usr/bin/env python3
"""
实盘运行脚本

启动wtpy_quant实盘交易系统
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from wtpy_quant.core import Wtpy<PERSON>ng<PERSON>, ConfigManager
from wtpy_quant.data import MarketDataAdapter
from wtpy_quant.exec import BrokerAdapter
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def main():
    """主函数"""
    try:
        logger.info("启动wtpy_quant实盘交易系统...")
        
        # 加载配置
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 创建引擎
        engine = WtpyEngine(config)
        
        # 初始化数据适配器
        market_adapter = MarketDataAdapter(config.data)
        
        # 初始化交易适配器
        broker_adapter = BrokerAdapter(config.trading)
        
        # 启动引擎
        await engine.start()
        
        logger.info("系统启动成功，开始运行...")
        
        # 保持运行
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭系统...")
    except Exception as e:
        logger.error(f"系统运行出错: {e}")
        raise
    finally:
        # 清理资源
        if 'engine' in locals():
            await engine.stop()
        logger.info("系统已停止")


if __name__ == "__main__":
    asyncio.run(main())
