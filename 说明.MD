## 基于 wtpy/wondertrader 的A股量化交易系统设计与开发说明

本说明文档描述一个使用 wtpy（基于 wondertrader C++ 内核）构建的全市场选股与交易系统。系统强调：
- 直接使用 wtpy 与 wondertrader 的高性能 C++ 核心（事件/撮合/时序处理）。
- 实盘数据与交易：集成 miniqmt（xtdata 实时行情 + 交易接口）。
- 数据层：短期高频缓存使用 mmap（最近1～7天），长期数据留有数据库写入接口（可后续对接 ClickHouse/PostgreSQL）。
- 策略：以 A 股全市场选股为主，支持 Tick 与 K 线来源的选股与排序。
- GUI：策略管理、回测分析、监控与配置。

参考：
- wondertrader/wtpy：https://github.com/wondertrader/wondertrader
- xtdata（miniqmt/xtquant 生态）：https://dict.thinktrader.net/nativeApi/xtdata.html
- xttrader miniqmt/xtquant 生态）： https://dict.thinktrader.net/nativeApi/xttrader.html?id=e2M5nZ

## 1. 目标与范围

- 市场范围：A 股（沪深），支持全市场级别的选股、排行、打分与下单。
- 策略形态：
  - 概念板块因子：基于每个概念板块的涨跌幅（Tick/K 线），筛选强势概念，再二次筛选成份股。
  - 微结构因子：如“前四笔 Tick 买卖量”排序，选出最强/最弱若干只。
- 运行形态：
  - 实盘：miniqmt 实时行情订阅 + 交易（下单、撤单、查持仓）。
  - 回测：基于 wtpy 的回测引擎（利用 C++ 内核），支持 Tick/Bar 多模式与成本模拟。
- 数据存储：
  - 短期：mmap 持久化环形缓冲（近1～7天），用于极速冷热查询与多进程共享。
  - 长期：数据库接口（抽象层，后续可接 ClickHouse/PostgreSQL），当前阶段可落地为 Parquet/CSV（作为占位与导入源）。


## 2. 技术架构

- 核心框架：
  - wtpy（Python）+ wondertrader（C++），遵循其事件、策略接口与最佳实践。
- 数据与交易：
  - miniqmt/xtdata：订阅全市场 Tick 与 K 线（多周期），下单/撤单/回报。
  - DataAdapter：将 xtdata/miniqmt 数据转换为 wtpy 所需结构并推入引擎。
- 存储：
  - 优先使用 wtpy/wondertrader 提供的内置数据/回测读写能力；仅在“当前 Tick 高频访问”场景下引入 MmapStore 作为补充。
  - MmapStore：按标的/频率/日期（today 优先）使用 numpy.memmap 管理环形块，提供零拷贝切片与 O(1) 最近K笔访问。
  - LongTermStore：抽象接口；当前支持 Parquet/CSV；预留 DB 写入器（ClickHouse/Postgres）。
- 运行与服务：
  - 后端服务：FastAPI（REST + WS）管理策略、订阅、账户、回测任务与监控。
  - GUI：Web（React/AntD，后续可 Electron/Tauri 封装）。
- 监控与日志：
  - 结构化日志 + 指标埋点（延迟、QPS、丢包、内存），Prometheus/Grafana 可选。
### 2.1 设计原则：优先复用 wtpy/wondertrader，仅在必要时扩展
- 原则1：不重复造轮子。策略生命周期、事件派发、撮合与回测、网关等核心能力优先直接使用 wtpy/wondertrader 官方实现。
- 原则2：最小化扩展。仅当官方能力暂不覆盖时，采用 Adapter/插件/二次封装补齐，保持与核心的解耦与向后兼容。
- 原则3：当前 Tick 优先。Tick 缓存以“当前交易日的高频访问”与“最近K笔O(1)读取”为核心，mmap 作为当天与近几日的高速缓存，长历史交由官方推荐的历史读写或数据库。
- 原则4：接口一致性。策略开发接口保持与 wtpy 策略基类一致；新增便捷能力通过 ctx 工具函数注入，避免破坏生态兼容。
- 原则5：配置沿用。尽量沿用 wondertrader 的配置/目录约定；新增字段以扩展方式提供，默认不影响原有配置。



## 3. 目录结构建议（初稿）

- app/
  - api/ （FastAPI 路由：策略、账户、回测、数据）
  - core/ （与 wtpy 交互：引擎装配、事件桥、策略管理）
  - data/ （DataAdapter、MmapStore、LongTermStore、概念与成份管理）
  - exec/ （TradeAdapter for miniqmt、OrderRouter、RiskEngine、Portfolio）
  - backtest/ （wtpy 回测集成、成本模型、报告生成）
  - strategy/ （策略模板、样例策略、指标工具）
  - gui/ （前端资源或独立仓）
  - config/ （系统/账户/策略配置模板）
  - tests/ （单元、集成、回归、性能测试）
  - scripts/ （数据导入、订阅、诊断）
  - docs/ （更多设计与 API 文档）


## 4. wtpy 集成要点

- 坚持“优先使用 wtpy/wondertrader 已有能力”：策略生命周期、事件模型、回测与撮合、数据/执行网关优先复用官方实现。
- 仅当官方特性无法满足需求时再扩展：通过 Adapter/插件与二次封装补齐（保持向下兼容，尽量不改动核心）。
- 使用 wtpy 的引擎与策略生命周期：on_init / on_tick / on_bar / on_order / on_trade / on_timer。
- 回测使用 wtpy 内置回测引擎（C++ 内核），与实盘保持一致的撮合与成本模型。
- 实盘扩展点：
  - MarketDataAdapter（miniqmt）：订阅/退订、转换数据结构并推入引擎事件。
  - BrokerAdapter（miniqmt）：下单、撤单、账户与持仓查询、成交回报转发。
- 配置与装配：
  - engine.config.json：策略、数据源、交易通道、风控规则与路径等；尽量沿用 wondertrader 的配置约定，新增项以扩展字段形式出现。


## 5. 数据层设计

### 5.1 实时行情（miniqmt/xtdata）
- 支持订阅级别：全市场 Tick；Bar（1m/5m/15m/日线等）。
- 异常与重连：心跳监测、断线重连后按时间戳游标补齐。
- 数据清洗：交易日历、停牌/复牌、除权除息、涨跌停价保护。

### 5.2 Mmap 短期缓存
- 目标：以“当前交易日 Tick 数据的高速访问与计算”为核心，辅以近1～7天的快速回放与交叉验证（多进程共享、零拷贝）。
- 组织与优先级：
  - 当日优先：data/mmap/{symbol}/tick/today.bin 为环形缓冲，容量覆盖全日 Tick；另存 today.idx（时间戳→偏移）。
  - 近期回放：data/mmap/{symbol}/tick/date=YYYYMMDD.bin + .idx（可选保留 1～7 天）。
  - Bar 同理：data/mmap/{symbol}/{freq}/today.bin 与按日分片。
- 数据结构：定长记录对齐（避免跨页拆分），写入采用单写者多读者模型（文件锁/原子游标）。
- 快速访问（核心场景）：
  - O(1) 获取“最近 K 笔 Tick”（如 K∈{1,4,20}），用于微结构因子计算与全市场横截面排序。
  - O(log N) 时间窗口检索（通过 .idx 二分定位），返回 memoryview/numpy 视图零拷贝切片。
- 清理与滚动：日切时将 today.bin 归档为 date=YYYYMMDD.bin；仅保留最近 N 天，过旧数据由 LongTermStore 接管（异步落库）。
- 说明：mmap 的定位是“当前数据优先”的高效缓存；长期分析与大规模回测走 LongTermStore/DB。

### 5.3 长期存储（占位 + 接口）
- LongTermStore 抽象：put_ticks/bars、query_ticks/bars、compact、backfill。
- 现状：默认写 Parquet（data/parquet/...），未来可切换为 ClickHouse/TDengin。
- 支持批量保存(包括实时录制)和批量查询等
### 5.4 概念/行业/成份管理
- SourceAdapter：对接概念板块与成份股来源（本地表/第三方API）。
- DataModel：concept(symbol->concepts，多对多)、concept_daily_perf。
- 聚合：分概念实时/区间涨跌幅、成交额、个股贡献榜单。


## 6. 交易执行与风险控制

- BrokerAdapter(miniqmt)：
  - place_order/cancel/query_positions/query_account。
  - 处理返回码与重试、幂等（避免重复下单）。
- OrderRouter：
  - 多账户/多通道路由，支持基于账户或策略维度的隔离。
- RiskEngine（前置 + 事后审计）：
  - 规则：单笔/单日限额、持仓上限、当日最大回撤、价格偏离保护、撤单限流。
  - 触发：拒单/降档/熔断；详细记录风险日志与指标。
- Portfolio/Account：
  - 多账户账本，浮动/已实现盈亏、保证金与可用资金计算，状态持久化可恢复。


## 7. 回测引擎

- 引擎：使用 wtpy 内置回测（C++ 内核），不重复造轮子。
- 模式：逐K/逐Tick/混合；
- 成本：手续费、滑点、印花税/过户费、撮合规则（与实盘尽量一致，优先用官方实现/配置）。
- 报告：年化、最大回撤、夏普、卡玛、胜率、收益回撤比、分板块贡献、交易明细（沿用 wtpy 报告能力，必要时扩展）。
- 数据：优先通过 wtpy 官方推荐方式读取历史；近端回测可从 mmap(today/最近几天)回放以验证微结构策略。


## 8. 策略框架与样例

- 统一模板：继承 wtpy 策略基类（或官方推荐模板），避免自造轮子；仅添加便捷 API 的轻量封装：
  - ctx.subscribe(symbols, ticks/bars)、ctx.get_ticks/bars、ctx.place_order/cancel（底层调用 wtpy 已实现接口）。
  - 因子工具：在不破坏 wtpy 数据结构的前提下提供工具函数（如最近K笔Tick获取、量价指标、概念聚合）。
  - 优先使用 wondertrader 内置指标/工具；自研部分以独立模块扩展，并保证可选关闭。

- 样例1：概念板块涨跌幅 → 成份股二次筛选（K线/日内）
  - 步骤：
    1) 聚合每个概念的区间涨跌幅与成交额（Bar/Tick 转 Bar）。
    2) 选Top N概念；
    3) 在每个概念中，对成份股按子因子（如当日涨幅、量比、换手）评分，取Top K；
    4) 生成候选池，下单与风控。

- 样例2：前四笔 Tick 买卖量排序（微结构，重度依赖“当前 Tick 缓存”）
  - 步骤：
    1) 从 mmap 的 today.bin（或 wtpy 当前快照）O(1) 获取每只股票最近4笔 Tick；
    2) 计算 buy_vol_sum - sell_vol_sum 或买卖量比；
    3) 全市场横截面排序取前 M；
    4) 结合价差/盘口过滤，触发下单或入池观察。
  - 注：该类策略强调“当前数据优先”，mmap 仅保存当天数据即能满足；历史仅用于验证与回测。


## 9. 性能与可靠性

- 目标：端到端数据到策略分发延迟 < 100ms（p99）。
- 手段：
  - C++ 内核 + Python 绑定（wtpy）；
  - 异步事件队列，慢策略隔离，背压与节流（丢冗余 Tick 或降采样）；
  - mmap 零拷贝访问；落盘异步；
  - 内存泄漏监控与对象池复用（热点路径）。
- 可靠性：
  - 断线重连、增量补齐；状态快照（账户/持仓/策略/订阅）；
  - 幂等操作、失败重试、熔断保护；
  - 结构化日志与指标上报，异常报警。

