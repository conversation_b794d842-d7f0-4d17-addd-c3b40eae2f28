from typing import List, Optional

try:
    from fastapi import APIRouter
    from pydantic import BaseModel
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    BaseModel = object  # type: ignore

router = APIRouter(prefix="/strategies", tags=["strategies"]) if APIRouter else None


class StrategyInfo(BaseModel):  # type: ignore[misc]
    name: str
    enabled: bool = False
    description: Optional[str] = None


# In-memory placeholder registry (will be backed by core.strategy_registry)
_REGISTRY: List[StrategyInfo] = []


if router:
    @router.get("/", response_model=List[StrategyInfo])  # type: ignore[arg-type]
    async def list_strategies():
        return _REGISTRY

    @router.post("/", response_model=StrategyInfo)  # type: ignore[arg-type]
    async def register_strategy(info: StrategyInfo):
        _REGISTRY.append(info)
        return info

    @router.post("/{name}/start")
    async def start_strategy(name: str):
        # Placeholder behavior: set enabled true
        for s in _REGISTRY:
            if s.name == name:
                s.enabled = True
        return {"ok": True, "name": name, "enabled": True}

    @router.post("/{name}/stop")
    async def stop_strategy(name: str):
        for s in _REGISTRY:
            if s.name == name:
                s.enabled = False
        return {"ok": True, "name": name, "enabled": False}

    class ParamUpdate(BaseModel):  # type: ignore[misc]
        key: str
        value: str

    @router.post("/{name}/param")
    async def set_param(name: str, update: ParamUpdate):
        # Placeholder: record a description update
        for s in _REGISTRY:
            if s.name == name:
                s.description = f"{s.description or ''} | {update.key}={update.value}".strip()
        return {"ok": True, "name": name, "param": {update.key: update.value}}

