"""
微结构因子策略 - 基于高频数据的微观结构因子策略

策略逻辑：
1. 基于Tick级别数据计算微观结构因子
2. 包括价差、深度、不平衡、流动性等因子
3. 短期预测价格方向和强度
4. 高频交易和快速进出
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import deque

from .base_strategy import BaseStrategy

logger = logging.getLogger(__name__)


class MicrostructureStrategy(BaseStrategy):
    """微结构因子策略"""
    
    def __init__(self, name: str = "MicrostructureStrategy", **kwargs):
        """
        初始化策略
        
        Args:
            name: 策略名称
            **kwargs: 策略参数
        """
        super().__init__(name, **kwargs)
        
        # 策略参数
        self.target_symbols = self.get_param("target_symbols", ["000001.SZ", "000002.SZ"])
        self.tick_window = self.get_param("tick_window", 100)  # Tick窗口大小
        self.signal_threshold = self.get_param("signal_threshold", 0.6)  # 信号阈值
        self.position_size = self.get_param("position_size", 1000)  # 单次交易股数
        self.max_position = self.get_param("max_position", 5000)  # 最大持仓
        self.hold_time = self.get_param("hold_time", 300)  # 最大持仓时间(秒)
        
        # 微结构因子参数
        self.spread_weight = self.get_param("spread_weight", 0.3)
        self.depth_weight = self.get_param("depth_weight", 0.2)
        self.imbalance_weight = self.get_param("imbalance_weight", 0.3)
        self.momentum_weight = self.get_param("momentum_weight", 0.2)
        
        # 数据缓存
        self.tick_buffers: Dict[str, deque] = {}
        self.factor_cache: Dict[str, Dict[str, float]] = {}
        
        # 订单跟踪
        self.active_orders: Dict[str, Dict] = {}
        self.order_times: Dict[str, datetime] = {}
        
        # 统计信息
        self.factor_stats = {
            'spread_signals': 0,
            'depth_signals': 0,
            'imbalance_signals': 0,
            'momentum_signals': 0,
            'combined_signals': 0
        }
        
        logger.info(f"MicrostructureStrategy initialized for {len(self.target_symbols)} symbols")
        
    def on_init(self, context: Any) -> None:
        """策略初始化"""
        try:
            self.log_info("微结构策略初始化开始")
            
            # 初始化数据缓存
            for symbol in self.target_symbols:
                self.tick_buffers[symbol] = deque(maxlen=self.tick_window)
                self.factor_cache[symbol] = {}
                
            # 订阅Tick数据
            for symbol in self.target_symbols:
                context.stra_sub_ticks(symbol)
                
            # 设置高频定时器
            context.stra_set_timer(1, 10)  # 每10秒检查一次
            
            self._initialized = True
            self.log_info("微结构策略初始化完成")
            
        except Exception as e:
            self.log_error(f"策略初始化失败: {e}")
            
    def on_tick(self, context: Any, stdCode: str, newTick: Any) -> None:
        """Tick数据回调"""
        try:
            if stdCode not in self.target_symbols:
                return
                
            # 添加到缓存
            tick_data = self._parse_tick_data(newTick)
            self.tick_buffers[stdCode].append(tick_data)
            
            # 计算微结构因子
            if len(self.tick_buffers[stdCode]) >= 20:  # 至少需要20个tick
                factors = self._calculate_microstructure_factors(stdCode)
                self.factor_cache[stdCode] = factors
                
                # 生成交易信号
                signal = self._generate_signal(stdCode, factors)
                if signal != 0:
                    self._execute_signal(context, stdCode, signal)
                    
        except Exception as e:
            self.log_error(f"处理Tick数据失败 {stdCode}: {e}")
            
    def on_bar(self, context: Any, stdCode: str, period: str, newBar: Any) -> None:
        """Bar数据回调"""
        # 微结构策略主要基于Tick数据，Bar数据用于辅助
        pass
        
    def on_timer(self, context: Any, id: int, times: int) -> None:
        """定时器回调"""
        try:
            if id == 1:
                # 检查持仓时间，强制平仓过期订单
                self._check_position_timeout(context)
                
                # 清理过期数据
                self._cleanup_expired_data()
                
        except Exception as e:
            self.log_error(f"定时器处理失败: {e}")
            
    def _parse_tick_data(self, tick: Any) -> Dict[str, Any]:
        """解析Tick数据"""
        return {
            'time': getattr(tick, 'actiontime', 0),
            'price': getattr(tick, 'price', 0),
            'volume': getattr(tick, 'volume', 0),
            'bid_price': getattr(tick, 'bidprice', [0])[0] if hasattr(tick, 'bidprice') else 0,
            'ask_price': getattr(tick, 'askprice', [0])[0] if hasattr(tick, 'askprice') else 0,
            'bid_volume': getattr(tick, 'bidqty', [0])[0] if hasattr(tick, 'bidqty') else 0,
            'ask_volume': getattr(tick, 'askqty', [0])[0] if hasattr(tick, 'askqty') else 0,
            'total_volume': getattr(tick, 'total_volume', 0),
            'turnover': getattr(tick, 'turnover', 0)
        }
        
    def _calculate_microstructure_factors(self, symbol: str) -> Dict[str, float]:
        """计算微结构因子"""
        try:
            ticks = list(self.tick_buffers[symbol])
            if len(ticks) < 10:
                return {}
                
            factors = {}
            
            # 1. 买卖价差因子
            factors['spread'] = self._calculate_spread_factor(ticks)
            
            # 2. 市场深度因子
            factors['depth'] = self._calculate_depth_factor(ticks)
            
            # 3. 订单不平衡因子
            factors['imbalance'] = self._calculate_imbalance_factor(ticks)
            
            # 4. 价格动量因子
            factors['momentum'] = self._calculate_momentum_factor(ticks)
            
            # 5. 成交量因子
            factors['volume'] = self._calculate_volume_factor(ticks)
            
            # 6. 波动率因子
            factors['volatility'] = self._calculate_volatility_factor(ticks)
            
            return factors
            
        except Exception as e:
            self.log_error(f"计算微结构因子失败 {symbol}: {e}")
            return {}
            
    def _calculate_spread_factor(self, ticks: List[Dict]) -> float:
        """计算买卖价差因子"""
        try:
            spreads = []
            for tick in ticks[-20:]:  # 使用最近20个tick
                bid = tick['bid_price']
                ask = tick['ask_price']
                if bid > 0 and ask > 0 and ask > bid:
                    spread = (ask - bid) / ((ask + bid) / 2)
                    spreads.append(spread)
                    
            if not spreads:
                return 0.0
                
            # 价差越小，流动性越好，给正分
            avg_spread = np.mean(spreads)
            recent_spread = spreads[-1] if spreads else avg_spread
            
            # 标准化：价差缩小为正信号
            return max(-1.0, min(1.0, (avg_spread - recent_spread) / (avg_spread + 1e-6)))
            
        except Exception as e:
            logger.error(f"计算价差因子失败: {e}")
            return 0.0
            
    def _calculate_depth_factor(self, ticks: List[Dict]) -> float:
        """计算市场深度因子"""
        try:
            depths = []
            for tick in ticks[-10:]:
                bid_vol = tick['bid_volume']
                ask_vol = tick['ask_volume']
                if bid_vol > 0 and ask_vol > 0:
                    total_depth = bid_vol + ask_vol
                    depths.append(total_depth)
                    
            if len(depths) < 2:
                return 0.0
                
            # 深度增加为正信号
            recent_depth = np.mean(depths[-3:]) if len(depths) >= 3 else depths[-1]
            avg_depth = np.mean(depths)
            
            return max(-1.0, min(1.0, (recent_depth - avg_depth) / (avg_depth + 1e-6)))
            
        except Exception as e:
            logger.error(f"计算深度因子失败: {e}")
            return 0.0
            
    def _calculate_imbalance_factor(self, ticks: List[Dict]) -> float:
        """计算订单不平衡因子"""
        try:
            imbalances = []
            for tick in ticks[-20:]:
                bid_vol = tick['bid_volume']
                ask_vol = tick['ask_volume']
                if bid_vol > 0 or ask_vol > 0:
                    total_vol = bid_vol + ask_vol
                    if total_vol > 0:
                        imbalance = (bid_vol - ask_vol) / total_vol
                        imbalances.append(imbalance)
                        
            if not imbalances:
                return 0.0
                
            # 买单占优为正信号
            return max(-1.0, min(1.0, np.mean(imbalances[-5:])))
            
        except Exception as e:
            logger.error(f"计算不平衡因子失败: {e}")
            return 0.0
            
    def _calculate_momentum_factor(self, ticks: List[Dict]) -> float:
        """计算价格动量因子"""
        try:
            prices = [tick['price'] for tick in ticks[-30:] if tick['price'] > 0]
            if len(prices) < 10:
                return 0.0
                
            # 计算短期和长期价格变化
            short_change = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0
            long_change = (prices[-1] - prices[-15]) / prices[-15] if len(prices) >= 15 else 0
            
            # 短期动量更重要
            momentum = 0.7 * short_change + 0.3 * long_change
            
            return max(-1.0, min(1.0, momentum * 100))  # 放大信号
            
        except Exception as e:
            logger.error(f"计算动量因子失败: {e}")
            return 0.0
            
    def _calculate_volume_factor(self, ticks: List[Dict]) -> float:
        """计算成交量因子"""
        try:
            volumes = []
            for i in range(1, len(ticks)):
                vol_diff = ticks[i]['total_volume'] - ticks[i-1]['total_volume']
                if vol_diff > 0:
                    volumes.append(vol_diff)
                    
            if len(volumes) < 5:
                return 0.0
                
            # 成交量放大为正信号
            recent_vol = np.mean(volumes[-3:]) if len(volumes) >= 3 else volumes[-1]
            avg_vol = np.mean(volumes)
            
            return max(-1.0, min(1.0, (recent_vol - avg_vol) / (avg_vol + 1e-6)))
            
        except Exception as e:
            logger.error(f"计算成交量因子失败: {e}")
            return 0.0
            
    def _calculate_volatility_factor(self, ticks: List[Dict]) -> float:
        """计算波动率因子"""
        try:
            prices = [tick['price'] for tick in ticks[-20:] if tick['price'] > 0]
            if len(prices) < 10:
                return 0.0
                
            returns = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    ret = (prices[i] - prices[i-1]) / prices[i-1]
                    returns.append(ret)
                    
            if len(returns) < 5:
                return 0.0
                
            # 波动率适中为好，过高过低都不好
            volatility = np.std(returns)
            
            # 将波动率映射到[-1, 1]区间
            # 适中的波动率(0.001-0.005)给正分
            if 0.001 <= volatility <= 0.005:
                return min(1.0, volatility / 0.003)
            elif volatility < 0.001:
                return -0.5  # 波动率过低
            else:
                return max(-1.0, -volatility / 0.01)  # 波动率过高
                
        except Exception as e:
            logger.error(f"计算波动率因子失败: {e}")
            return 0.0
            
    def _generate_signal(self, symbol: str, factors: Dict[str, float]) -> int:
        """生成交易信号"""
        try:
            if not factors:
                return 0
                
            # 加权合成信号
            signal_score = 0.0
            
            signal_score += factors.get('spread', 0) * self.spread_weight
            signal_score += factors.get('depth', 0) * self.depth_weight
            signal_score += factors.get('imbalance', 0) * self.imbalance_weight
            signal_score += factors.get('momentum', 0) * self.momentum_weight
            
            # 成交量和波动率作为过滤条件
            volume_factor = factors.get('volume', 0)
            volatility_factor = factors.get('volatility', 0)
            
            # 成交量不足或波动率异常时不交易
            if volume_factor < -0.5 or volatility_factor < -0.5:
                return 0
                
            # 信号强度判断
            if signal_score > self.signal_threshold:
                self.factor_stats['combined_signals'] += 1
                return 1  # 买入信号
            elif signal_score < -self.signal_threshold:
                self.factor_stats['combined_signals'] += 1
                return -1  # 卖出信号
            else:
                return 0  # 无信号
                
        except Exception as e:
            self.log_error(f"生成信号失败 {symbol}: {e}")
            return 0
            
    def _execute_signal(self, context: Any, symbol: str, signal: int) -> None:
        """执行交易信号"""
        try:
            current_pos = self.get_position(context, symbol)
            
            # 检查是否超过最大持仓
            if signal > 0 and current_pos >= self.max_position:
                return
            if signal < 0 and current_pos <= -self.max_position:
                return
                
            # 执行交易
            if signal > 0:
                # 买入
                if self.buy(context, symbol, self.position_size):
                    self.order_times[f"{symbol}_buy"] = datetime.now()
                    self.log_info(f"微结构买入信号: {symbol} {self.position_size}")
                    
            elif signal < 0:
                # 卖出
                if current_pos > 0:
                    # 平多仓
                    qty = min(self.position_size, current_pos)
                    if self.sell(context, symbol, qty):
                        self.order_times[f"{symbol}_sell"] = datetime.now()
                        self.log_info(f"微结构卖出信号: {symbol} {qty}")
                else:
                    # 开空仓（如果支持）
                    if self.sell(context, symbol, self.position_size):
                        self.order_times[f"{symbol}_short"] = datetime.now()
                        self.log_info(f"微结构做空信号: {symbol} {self.position_size}")
                        
        except Exception as e:
            self.log_error(f"执行信号失败 {symbol}: {e}")
            
    def _check_position_timeout(self, context: Any) -> None:
        """检查持仓超时"""
        try:
            current_time = datetime.now()
            
            for symbol in self.target_symbols:
                position = self.get_position(context, symbol)
                if abs(position) < 1:
                    continue
                    
                # 检查是否超时
                order_key = f"{symbol}_{'buy' if position > 0 else 'sell'}"
                order_time = self.order_times.get(order_key)
                
                if order_time and (current_time - order_time).total_seconds() > self.hold_time:
                    # 强制平仓
                    if position > 0:
                        self.sell(context, symbol, position)
                        self.log_info(f"超时平多仓: {symbol} {position}")
                    else:
                        self.buy(context, symbol, abs(position))
                        self.log_info(f"超时平空仓: {symbol} {abs(position)}")
                        
                    # 清理记录
                    if order_key in self.order_times:
                        del self.order_times[order_key]
                        
        except Exception as e:
            self.log_error(f"检查持仓超时失败: {e}")
            
    def _cleanup_expired_data(self) -> None:
        """清理过期数据"""
        try:
            current_time = datetime.now()
            
            # 清理过期的订单时间记录
            expired_keys = []
            for key, order_time in self.order_times.items():
                if (current_time - order_time).total_seconds() > self.hold_time * 2:
                    expired_keys.append(key)
                    
            for key in expired_keys:
                del self.order_times[key]
                
        except Exception as e:
            self.log_error(f"清理过期数据失败: {e}")
            
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = {
            'name': self.name,
            'type': 'microstructure',
            'target_symbols': self.target_symbols,
            'tick_window': self.tick_window,
            'signal_threshold': self.signal_threshold,
            'active_positions': len([s for s in self.target_symbols 
                                   if abs(self._positions.get(s, 0)) > 0]),
            'factor_stats': self.factor_stats.copy()
        }
        info.update(self.get_stats())
        return info
