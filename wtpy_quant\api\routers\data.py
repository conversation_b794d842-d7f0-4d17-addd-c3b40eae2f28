"""
数据服务API路由

提供数据相关的REST API接口，包括：
- 市场数据查询
- 概念板块数据
- 技术指标计算
- 数据统计信息
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date, timedelta

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from ...data.concept_manager import ConceptManager
from ...data.mmap_store import MmapStore
from ...strategy.indicators import sma, ema, rsi, macd, bollinger_bands

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic模型定义
class MarketDataRequest(BaseModel):
    """市场数据请求"""
    symbols: List[str] = Field(..., description="股票代码列表")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")
    frequency: str = Field("1d", description="数据频率: 1m, 5m, 15m, 30m, 1h, 1d")


class IndicatorRequest(BaseModel):
    """技术指标请求"""
    symbol: str = Field(..., description="股票代码")
    indicator: str = Field(..., description="指标名称")
    period: int = Field(20, description="计算周期")
    start_date: date = Field(..., description="开始日期")
    end_date: date = Field(..., description="结束日期")


class ConceptPerformance(BaseModel):
    """概念表现"""
    concept_code: str
    concept_name: str
    change_pct: float
    constituent_count: int
    avg_turnover: float
    update_time: str


# 依赖注入
def get_concept_manager() -> ConceptManager:
    """获取概念管理器"""
    from ...api.main import app_state
    if not app_state['initialized'] or not app_state['concept_manager']:
        raise HTTPException(status_code=503, detail="Concept manager not available")
    return app_state['concept_manager']


@router.get("/market/symbols")
async def get_available_symbols():
    """
    获取可用的股票代码列表
    """
    try:
        # 这里应该从数据源获取，简化处理返回常用股票
        symbols = [
            {"symbol": "000001.SZ", "name": "平安银行", "market": "SZ"},
            {"symbol": "000002.SZ", "name": "万科A", "market": "SZ"},
            {"symbol": "600000.SH", "name": "浦发银行", "market": "SH"},
            {"symbol": "600036.SH", "name": "招商银行", "market": "SH"},
            {"symbol": "000858.SZ", "name": "五粮液", "market": "SZ"},
            {"symbol": "600519.SH", "name": "贵州茅台", "market": "SH"}
        ]
        
        return {"symbols": symbols}
        
    except Exception as e:
        logger.error(f"Error getting available symbols: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/market/bars")
async def get_market_bars(request: MarketDataRequest):
    """
    获取K线数据
    """
    try:
        # 验证参数
        if request.start_date >= request.end_date:
            raise HTTPException(status_code=400, detail="Start date must be before end date")
            
        if request.frequency not in ["1m", "5m", "15m", "30m", "1h", "1d"]:
            raise HTTPException(status_code=400, detail="Invalid frequency")
            
        # 这里应该从实际数据源获取数据
        # 简化处理，返回模拟数据
        result = {}
        
        for symbol in request.symbols:
            # 生成模拟K线数据
            days = (request.end_date - request.start_date).days
            dates = [request.start_date + timedelta(days=i) for i in range(days + 1)]
            
            bars = []
            base_price = 10.0
            
            for i, date in enumerate(dates):
                price = base_price + (i * 0.01)  # 简单的价格变化
                bar = {
                    "datetime": date.isoformat(),
                    "open": round(price, 2),
                    "high": round(price * 1.02, 2),
                    "low": round(price * 0.98, 2),
                    "close": round(price, 2),
                    "volume": 1000000 + (i * 10000)
                }
                bars.append(bar)
                
            result[symbol] = bars
            
        return {"data": result, "frequency": request.frequency}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting market bars: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/market/ticks/{symbol}")
async def get_market_ticks(
    symbol: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = Query(1000, le=10000)
):
    """
    获取Tick数据
    """
    try:
        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)
            
        # 这里应该从实际数据源获取Tick数据
        # 简化处理，返回模拟数据
        ticks = []
        
        current_time = start_time
        base_price = 10.0
        
        while current_time <= end_time and len(ticks) < limit:
            tick = {
                "datetime": current_time.isoformat(),
                "price": round(base_price + (len(ticks) * 0.001), 3),
                "volume": 100,
                "bid_price": round(base_price + (len(ticks) * 0.001) - 0.01, 3),
                "ask_price": round(base_price + (len(ticks) * 0.001) + 0.01, 3),
                "bid_volume": 1000,
                "ask_volume": 1000
            }
            ticks.append(tick)
            current_time += timedelta(seconds=3)  # 3秒间隔
            
        return {"symbol": symbol, "ticks": ticks}
        
    except Exception as e:
        logger.error(f"Error getting market ticks for {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/indicators/calculate")
async def calculate_indicator(request: IndicatorRequest):
    """
    计算技术指标
    """
    try:
        # 首先获取价格数据（这里简化处理）
        days = (request.end_date - request.start_date).days
        dates = [request.start_date + timedelta(days=i) for i in range(days + 1)]
        
        # 生成模拟价格数据
        import pandas as pd
        import numpy as np
        
        prices = pd.Series([10.0 + i * 0.01 + np.random.normal(0, 0.1) for i in range(len(dates))])
        
        # 计算指标
        result = {"symbol": request.symbol, "indicator": request.indicator, "period": request.period}
        
        if request.indicator.lower() == "sma":
            values = sma(prices, request.period)
            result["values"] = values.dropna().tolist()
            
        elif request.indicator.lower() == "ema":
            values = ema(prices, request.period)
            result["values"] = values.dropna().tolist()
            
        elif request.indicator.lower() == "rsi":
            values = rsi(prices, request.period)
            result["values"] = values.dropna().tolist()
            
        elif request.indicator.lower() == "macd":
            macd_line, signal_line, histogram = macd(prices)
            result["macd_line"] = macd_line.dropna().tolist()
            result["signal_line"] = signal_line.dropna().tolist()
            result["histogram"] = histogram.dropna().tolist()
            
        elif request.indicator.lower() == "bb":
            upper, middle, lower = bollinger_bands(prices, request.period)
            result["upper"] = upper.dropna().tolist()
            result["middle"] = middle.dropna().tolist()
            result["lower"] = lower.dropna().tolist()
            
        else:
            raise HTTPException(status_code=400, detail="Unsupported indicator")
            
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating indicator: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/concepts/", response_model=List[ConceptPerformance])
async def get_concepts(
    limit: int = Query(50, le=200),
    sort_by: str = Query("change_pct", regex="^(change_pct|constituent_count|avg_turnover)$"),
    concept_manager: ConceptManager = Depends(get_concept_manager)
):
    """
    获取概念板块列表
    """
    try:
        concepts = concept_manager.get_top_concepts(limit=limit)
        
        result = []
        for concept in concepts:
            performance = ConceptPerformance(
                concept_code=concept.concept_code,
                concept_name=concept.concept_name,
                change_pct=concept.change_pct,
                constituent_count=concept.constituent_count,
                avg_turnover=concept.avg_turnover,
                update_time=concept.update_time.isoformat()
            )
            result.append(performance)
            
        # 排序
        if sort_by == "change_pct":
            result.sort(key=lambda x: x.change_pct, reverse=True)
        elif sort_by == "constituent_count":
            result.sort(key=lambda x: x.constituent_count, reverse=True)
        elif sort_by == "avg_turnover":
            result.sort(key=lambda x: x.avg_turnover, reverse=True)
            
        return result
        
    except Exception as e:
        logger.error(f"Error getting concepts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/concepts/{concept_code}/constituents")
async def get_concept_constituents(
    concept_code: str,
    concept_manager: ConceptManager = Depends(get_concept_manager)
):
    """
    获取概念成分股
    """
    try:
        constituents = concept_manager.get_concept_constituents(concept_code)
        
        result = []
        for constituent in constituents:
            result.append({
                "symbol": constituent.symbol,
                "name": constituent.name,
                "weight": constituent.weight,
                "market_cap": constituent.market_cap,
                "industry": constituent.industry
            })
            
        return {"concept_code": concept_code, "constituents": result}
        
    except Exception as e:
        logger.error(f"Error getting concept constituents for {concept_code}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/concepts/search")
async def search_concepts(
    keyword: str = Query(..., min_length=1),
    limit: int = Query(20, le=100),
    concept_manager: ConceptManager = Depends(get_concept_manager)
):
    """
    搜索概念板块
    """
    try:
        # 这里应该实现实际的搜索逻辑
        # 简化处理，返回包含关键词的概念
        all_concepts = concept_manager.get_top_concepts(limit=1000)
        
        matched_concepts = []
        for concept in all_concepts:
            if keyword.lower() in concept.concept_name.lower():
                matched_concepts.append({
                    "concept_code": concept.concept_code,
                    "concept_name": concept.concept_name,
                    "change_pct": concept.change_pct,
                    "constituent_count": concept.constituent_count
                })
                
            if len(matched_concepts) >= limit:
                break
                
        return {"keyword": keyword, "results": matched_concepts}
        
    except Exception as e:
        logger.error(f"Error searching concepts with keyword {keyword}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/symbols/{symbol}/concepts")
async def get_symbol_concepts(
    symbol: str,
    concept_manager: ConceptManager = Depends(get_concept_manager)
):
    """
    获取股票所属概念
    """
    try:
        concepts = concept_manager.get_symbol_concepts(symbol)
        
        result = []
        for concept_code in concepts:
            # 获取概念详细信息
            concept_info = concept_manager.get_concept_info(concept_code)
            if concept_info:
                result.append({
                    "concept_code": concept_code,
                    "concept_name": concept_info.concept_name,
                    "change_pct": concept_info.change_pct
                })
                
        return {"symbol": symbol, "concepts": result}
        
    except Exception as e:
        logger.error(f"Error getting concepts for symbol {symbol}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/data")
async def get_data_stats():
    """
    获取数据统计信息
    """
    try:
        # 这里应该从实际数据源获取统计信息
        # 简化处理，返回模拟统计
        stats = {
            "market_data": {
                "total_symbols": 4000,
                "active_symbols": 3800,
                "last_update": datetime.now().isoformat()
            },
            "tick_data": {
                "total_ticks_today": 1500000,
                "avg_ticks_per_symbol": 375,
                "last_tick_time": datetime.now().isoformat()
            },
            "concepts": {
                "total_concepts": 300,
                "active_concepts": 280,
                "avg_constituents": 25
            },
            "storage": {
                "total_size_gb": 150.5,
                "daily_growth_gb": 2.3,
                "retention_days": 365
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting data stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/indicators/supported")
async def get_supported_indicators():
    """
    获取支持的技术指标列表
    """
    return {
        "indicators": [
            {
                "name": "sma",
                "display_name": "简单移动平均",
                "description": "Simple Moving Average",
                "parameters": [{"name": "period", "type": "int", "default": 20}]
            },
            {
                "name": "ema", 
                "display_name": "指数移动平均",
                "description": "Exponential Moving Average",
                "parameters": [{"name": "period", "type": "int", "default": 20}]
            },
            {
                "name": "rsi",
                "display_name": "相对强弱指标",
                "description": "Relative Strength Index",
                "parameters": [{"name": "period", "type": "int", "default": 14}]
            },
            {
                "name": "macd",
                "display_name": "MACD指标",
                "description": "Moving Average Convergence Divergence",
                "parameters": [
                    {"name": "fast_period", "type": "int", "default": 12},
                    {"name": "slow_period", "type": "int", "default": 26},
                    {"name": "signal_period", "type": "int", "default": 9}
                ]
            },
            {
                "name": "bb",
                "display_name": "布林带",
                "description": "Bollinger Bands",
                "parameters": [
                    {"name": "period", "type": "int", "default": 20},
                    {"name": "std_dev", "type": "float", "default": 2.0}
                ]
            }
        ]
    }
