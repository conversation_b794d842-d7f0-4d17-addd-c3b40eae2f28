"""
数据层测试
"""

import pytest
import asyncio
import tempfile
import numpy as np
from pathlib import Path
from unittest.mock import MagicMock, patch

from wtpy_quant.data import MarketDataAdapter, MmapStore, LongTermStore, ConceptManager
from wtpy_quant.data.market_adapter import TickData, BarData
from wtpy_quant.data.mmap_store import MmapConfig, MmapBuffer, TICK_DTYPE, BAR_DTYPE
from wtpy_quant.data.concept_manager import ConceptInfo, ConceptConstituent


class TestMarketDataAdapter:
    """市场数据适配器测试"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        adapter = MarketDataAdapter()
        
        # Mock xtdata不可用的情况
        with patch.object(adapter, '_check_xtdata_available', return_value=False):
            success = await adapter.initialize()
            assert not success
            
    def test_tick_data_conversion(self):
        """测试Tick数据转换"""
        # 创建模拟的xtdata tick对象
        mock_tick = MagicMock()
        mock_tick.stock_code = "000001.SZ"
        mock_tick.time = "20231201 09:30:00"
        mock_tick.last_price = 10.50
        mock_tick.volume = 1000
        mock_tick.amount = 10500.0
        mock_tick.bid_price = [10.49]
        mock_tick.ask_price = [10.51]
        mock_tick.bid_volume = [500]
        mock_tick.ask_volume = [800]
        mock_tick.open_interest = 0
        
        # 转换数据
        tick_data = TickData.from_xtdata(mock_tick)
        
        assert tick_data.symbol == "000001.SZ"
        assert tick_data.datetime == "20231201 09:30:00"
        assert tick_data.price == 10.50
        assert tick_data.volume == 1000
        assert tick_data.turnover == 10500.0
        assert tick_data.bid_price == 10.49
        assert tick_data.ask_price == 10.51
        
    def test_bar_data_conversion(self):
        """测试Bar数据转换"""
        # 创建模拟的xtdata bar对象
        mock_bar = MagicMock()
        mock_bar.stock_code = "000001.SZ"
        mock_bar.time = "20231201 09:30:00"
        mock_bar.open = 10.45
        mock_bar.high = 10.55
        mock_bar.low = 10.40
        mock_bar.close = 10.50
        mock_bar.volume = 100000
        mock_bar.amount = 1050000.0
        
        # 转换数据
        bar_data = BarData.from_xtdata(mock_bar, "1m")
        
        assert bar_data.symbol == "000001.SZ"
        assert bar_data.datetime == "20231201 09:30:00"
        assert bar_data.period == "1m"
        assert bar_data.open == 10.45
        assert bar_data.high == 10.55
        assert bar_data.low == 10.40
        assert bar_data.close == 10.50
        assert bar_data.volume == 100000
        assert bar_data.turnover == 1050000.0


class TestMmapStore:
    """Mmap存储测试"""
    
    def test_mmap_buffer_creation(self, temp_dir):
        """测试Mmap缓冲区创建"""
        file_path = temp_dir / "test_buffer.bin"
        buffer = MmapBuffer(file_path, TICK_DTYPE, 1000)
        
        assert buffer.file_path == file_path
        assert buffer.dtype == TICK_DTYPE
        assert buffer.buffer_size == 1000
        
        # 测试打开
        success = buffer.open()
        assert success
        assert file_path.exists()
        
        # 测试关闭
        buffer.close()
        
    def test_mmap_buffer_append_and_get(self, temp_dir):
        """测试数据追加和获取"""
        file_path = temp_dir / "test_buffer.bin"
        buffer = MmapBuffer(file_path, TICK_DTYPE, 100)
        
        buffer.open()
        
        # 创建测试数据
        test_records = []
        for i in range(10):
            record = np.array([(
                1700000000.0 + i,  # timestamp
                10.0 + i * 0.1,     # price
                1000 + i,           # volume
                10000.0 + i * 100,  # turnover
                9.9 + i * 0.1,      # bid_price
                10.1 + i * 0.1,     # ask_price
                500 + i,            # bid_volume
                600 + i,            # ask_volume
                0                   # open_interest
            )], dtype=TICK_DTYPE)
            
            success = buffer.append(record[0])
            assert success
            test_records.append(record[0])
        
        # 测试获取最近的记录
        recent_5 = buffer.get_recent(5)
        assert recent_5 is not None
        assert len(recent_5) == 5
        
        # 验证数据正确性
        for i, record in enumerate(recent_5):
            expected_price = 10.0 + (5 + i) * 0.1
            assert abs(record['price'] - expected_price) < 0.001
        
        # 测试获取所有记录
        all_records = buffer.get_recent(20)  # 超过实际数量
        assert len(all_records) == 10
        
        buffer.close()
        
    def test_mmap_store_operations(self, temp_dir):
        """测试Mmap存储操作"""
        config = MmapConfig(base_path=str(temp_dir), tick_buffer_size=1000)
        store = MmapStore(config)
        
        # 创建测试Tick数据
        tick_data = TickData(
            symbol="000001.SZ",
            datetime="20231201 09:30:00",
            price=10.50,
            volume=1000,
            turnover=10500.0,
            bid_price=10.49,
            ask_price=10.51,
            bid_volume=500,
            ask_volume=800
        )
        
        # 存储数据
        success = store.store_tick(tick_data)
        assert success
        
        # 获取数据
        recent_ticks = store.get_recent_ticks("000001.SZ", 1)
        assert recent_ticks is not None
        assert len(recent_ticks) == 1
        
        # 验证数据
        stored_tick = recent_ticks[0]
        assert abs(stored_tick['price'] - 10.50) < 0.001
        assert stored_tick['volume'] == 1000
        
        # 清理
        store.close_all()


class TestLongTermStore:
    """长期存储测试"""
    
    @pytest.mark.asyncio
    async def test_parquet_store_operations(self, temp_dir):
        """测试Parquet存储操作"""
        store = LongTermStore("parquet", {"path": str(temp_dir)})
        
        # 创建测试数据
        tick_data = [
            TickData(
                symbol="000001.SZ",
                datetime="20231201 09:30:00",
                price=10.50,
                volume=1000,
                turnover=10500.0,
                bid_price=10.49,
                ask_price=10.51,
                bid_volume=500,
                ask_volume=800
            ),
            TickData(
                symbol="000001.SZ",
                datetime="20231201 09:31:00",
                price=10.52,
                volume=1200,
                turnover=12624.0,
                bid_price=10.51,
                ask_price=10.53,
                bid_volume=600,
                ask_volume=700
            )
        ]
        
        # 存储数据
        success = await store.put_ticks("000001.SZ", tick_data, "20231201")
        assert success
        
        # 查询数据
        queried_ticks = await store.query_ticks("000001.SZ", "20231201", "20231201")
        assert len(queried_ticks) == 2
        
        # 验证数据
        assert queried_ticks[0].symbol == "000001.SZ"
        assert queried_ticks[0].price == 10.50
        assert queried_ticks[1].price == 10.52
        
    @pytest.mark.asyncio
    async def test_bar_storage(self, temp_dir):
        """测试Bar数据存储"""
        store = LongTermStore("parquet", {"path": str(temp_dir)})
        
        # 创建测试Bar数据
        bar_data = [
            BarData(
                symbol="000001.SZ",
                datetime="20231201 09:30:00",
                period="1m",
                open=10.45,
                high=10.55,
                low=10.40,
                close=10.50,
                volume=100000,
                turnover=1050000.0
            )
        ]
        
        # 存储数据
        success = await store.put_bars("000001.SZ", bar_data, "1m", "20231201")
        assert success
        
        # 查询数据
        queried_bars = await store.query_bars("000001.SZ", "1m", "20231201", "20231201")
        assert len(queried_bars) == 1
        
        # 验证数据
        bar = queried_bars[0]
        assert bar.symbol == "000001.SZ"
        assert bar.period == "1m"
        assert bar.open == 10.45
        assert bar.close == 10.50


class TestConceptManager:
    """概念管理器测试"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        manager = ConceptManager("local")
        success = await manager.initialize()
        assert success
        
        # 验证加载的数据
        concepts = manager.get_all_concepts()
        assert len(concepts) > 0
        
        # 验证概念信息
        ai_concept = manager.get_concept_info("AI")
        assert ai_concept is not None
        assert ai_concept.name == "人工智能"
        
    @pytest.mark.asyncio
    async def test_concept_constituents(self):
        """测试概念成份股"""
        manager = ConceptManager("local")
        await manager.initialize()
        
        # 获取概念成份股
        ai_constituents = manager.get_concept_constituents("AI")
        assert len(ai_constituents) > 0
        
        # 验证成份股信息
        for constituent in ai_constituents:
            assert constituent.concept_code == "AI"
            assert constituent.symbol
            assert constituent.weight > 0
            
        # 测试股票所属概念
        symbol_concepts = manager.get_symbol_concepts("000001.SZ")
        assert len(symbol_concepts) > 0
        assert "AI" in symbol_concepts
        
    @pytest.mark.asyncio
    async def test_concept_performance_calculation(self):
        """测试概念表现计算"""
        manager = ConceptManager("local")
        await manager.initialize()
        
        # 更新一些价格数据
        manager.update_price("000001.SZ", 10.50)
        manager.update_price("000002.SZ", 20.30)
        manager.update_price("600000.SH", 15.80)
        
        # 计算概念表现
        performance = manager.calculate_concept_performance("AI")
        assert performance is not None
        assert performance.concept_code == "AI"
        assert performance.constituent_count > 0
        
    @pytest.mark.asyncio
    async def test_top_concepts(self):
        """测试获取热门概念"""
        manager = ConceptManager("local")
        await manager.initialize()
        
        # 更新价格数据
        test_symbols = ["000001.SZ", "000002.SZ", "600000.SH", "600001.SH", "000003.SZ"]
        for i, symbol in enumerate(test_symbols):
            manager.update_price(symbol, 10.0 + i)
        
        # 获取热门概念
        top_concepts = manager.get_top_concepts(limit=3)
        assert len(top_concepts) <= 3
        
        # 验证排序
        if len(top_concepts) > 1:
            assert top_concepts[0].change_pct >= top_concepts[1].change_pct


if __name__ == "__main__":
    pytest.main([__file__])
