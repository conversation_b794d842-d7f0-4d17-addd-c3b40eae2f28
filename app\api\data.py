from typing import Literal

try:
    from fastapi import APIRouter, Query
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    Query = None  # type: ignore

router = APIRouter() if APIRouter else None


if router:
    @router.get("/tick")
    async def get_tick(symbol: str = Query(..., description="e.g., SH600000")):
        # Placeholder: in future, fetch from MmapStore or wtpy snapshot
        return {"symbol": symbol, "last_price": None, "ts": None}

    @router.get("/bar")
    async def get_bar(symbol: str, freq: Literal["1m", "5m", "15m", "1d"] = "1m"):
        # Placeholder
        return {"symbol": symbol, "freq": freq, "bars": []}

