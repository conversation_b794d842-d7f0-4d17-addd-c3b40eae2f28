# wtpy_quant 使用指南

本文档详细介绍如何使用wtpy_quant量化交易系统。

## 目录

1. [系统架构](#系统架构)
2. [配置说明](#配置说明)
3. [策略开发](#策略开发)
4. [回测使用](#回测使用)
5. [实盘交易](#实盘交易)
6. [API使用](#api使用)
7. [监控和风控](#监控和风控)
8. [常见问题](#常见问题)

## 系统架构

wtpy_quant采用模块化设计，主要包含以下组件：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web API       │    │   策略管理      │    │   风险控制      │
│   (FastAPI)     │    │  (Strategy)     │    │  (Risk Engine)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    wtpy引擎核心 (WtpyEngine)                      │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据层        │    │   交易执行      │    │   回测引擎      │
│  (Data Layer)   │    │  (Execution)    │    │  (Backtest)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 配置说明

### 主配置文件

系统使用YAML格式的配置文件，位于 `config/config.yaml`：

```yaml
# 引擎配置
engine:
  mode: "live"              # 运行模式: live/backtest
  data_path: "./data"       # 数据路径
  log_level: "INFO"         # 日志级别

# 数据配置
data:
  source: "xtdata"          # 数据源: xtdata/mock
  path: "./data"            # 数据存储路径
  cache_size: 10000         # 缓存大小
  symbols: []               # 订阅股票列表，空表示全市场

# 交易配置
trading:
  broker: "xttrader"        # 交易接口: xttrader/mock
  accounts:                 # 交易账户配置
    - account_id: "123456"
      account_type: "stock"
      
# 风险配置
risk:
  max_position_ratio: 0.1   # 最大单仓位比例
  max_drawdown: 0.1         # 最大回撤限制
  daily_loss_limit: 0.05    # 日内亏损限制
  
# API配置
api:
  host: "127.0.0.1"
  port: 8000
  cors_origins: ["*"]
  
# 回测配置
backtest:
  data_path: "./backtest_data"
  commission_rate: 0.0003   # 手续费率
  slippage: 0.001          # 滑点
```

### 环境变量

可以通过环境变量覆盖配置：

```bash
export WTPY_QUANT_DATA_PATH="/path/to/data"
export WTPY_QUANT_API_PORT="8080"
export WTPY_QUANT_LOG_LEVEL="DEBUG"
```

## 策略开发

### 策略基类

所有策略都需要继承 `BaseStrategy` 类：

```python
from wtpy_quant.strategy import BaseStrategy

class MyStrategy(BaseStrategy):
    def __init__(self, name="MyStrategy", **kwargs):
        super().__init__(name, **kwargs)
        
        # 策略参数
        self.lookback_period = self.get_param("lookback_period", 20)
        self.threshold = self.get_param("threshold", 0.02)
        
    def on_init(self, context):
        """策略初始化"""
        self.log_info("策略初始化完成")
        
        # 订阅数据
        context.stra_sub_ticks("000001.SZ")
        
        # 设置定时器
        context.stra_set_timer(1, 60)  # 每分钟执行一次
        
    def on_tick(self, context, stdCode, newTick):
        """Tick数据回调"""
        price = newTick.price
        # 处理实时数据
        
    def on_bar(self, context, stdCode, period, newBar):
        """K线数据回调"""
        if period != "m1":
            return
            
        # 获取历史数据
        bars = self.get_bars(context, stdCode, "m1", self.lookback_period)
        
        # 计算指标
        from wtpy_quant.strategy.indicators import sma, rsi
        
        close_prices = bars['close']
        ma = sma(close_prices, 10)
        rsi_val = rsi(close_prices, 14)
        
        # 生成交易信号
        if rsi_val.iloc[-1] < 30:  # 超卖
            self.buy(context, stdCode, 1000)
        elif rsi_val.iloc[-1] > 70:  # 超买
            self.sell(context, stdCode, 1000)
            
    def on_timer(self, context, id, times):
        """定时器回调"""
        if id == 1:
            # 定期检查持仓
            positions = self.get_positions()
            self.log_info(f"当前持仓: {positions}")
```

### 技术指标使用

系统提供丰富的技术指标库：

```python
from wtpy_quant.strategy.indicators import (
    sma, ema, rsi, macd, bollinger_bands, 
    kdj, atr, obv, cci
)

# 移动平均
ma5 = sma(close_prices, 5)
ma20 = sma(close_prices, 20)

# RSI指标
rsi14 = rsi(close_prices, 14)

# MACD指标
macd_line, signal_line, histogram = macd(close_prices)

# 布林带
upper, middle, lower = bollinger_bands(close_prices, 20, 2.0)

# KDJ指标
k, d, j = kdj(high_prices, low_prices, close_prices)
```

### 样例策略

#### 概念轮动策略

```python
from wtpy_quant.strategy import ConceptStrategy

strategy = ConceptStrategy(
    name="概念轮动策略",
    max_positions=10,           # 最大持仓数
    position_size=0.1,          # 单仓位大小
    concept_lookback=5,         # 概念表现回看天数
    min_concept_return=0.02,    # 最小概念涨幅
    stop_loss=-0.05,           # 止损比例
    take_profit=0.15           # 止盈比例
)
```

#### 微结构策略

```python
from wtpy_quant.strategy import MicrostructureStrategy

strategy = MicrostructureStrategy(
    name="微结构策略",
    target_symbols=["000001.SZ", "000002.SZ"],
    tick_window=100,            # Tick窗口大小
    signal_threshold=0.6,       # 信号阈值
    position_size=1000,         # 单次交易股数
    hold_time=300              # 最大持仓时间(秒)
)
```

## 回测使用

### 创建回测任务

```python
from wtpy_quant.backtest import BacktestEngine, BacktestTask
from datetime import date

# 创建回测引擎
backtest_engine = BacktestEngine(config.backtest)

# 创建回测任务
task = BacktestTask(
    name="策略回测",
    strategy_type="ConceptStrategy",
    strategy_params={
        "max_positions": 10,
        "position_size": 0.1
    },
    start_date=date(2023, 1, 1),
    end_date=date(2023, 12, 31),
    initial_capital=1000000.0,
    symbols=["000001.SZ", "000002.SZ"],  # 可选，空表示全市场
    benchmark="000300.SH"
)

# 运行回测
task_id = await backtest_engine.submit_task(task)
result = await backtest_engine.run_task(task_id)

# 查看结果
print(f"总收益率: {result.performance_metrics['total_return']:.2%}")
print(f"年化收益率: {result.performance_metrics['annual_return']:.2%}")
print(f"最大回撤: {result.performance_metrics['max_drawdown']:.2%}")
print(f"夏普比率: {result.performance_metrics['sharpe_ratio']:.2f}")
```

### 生成回测报告

```python
from wtpy_quant.backtest import ReportGenerator

# 生成HTML报告
report_generator = ReportGenerator()
html_report = await report_generator.generate_html_report(result)

# 保存报告
with open("backtest_report.html", "w", encoding="utf-8") as f:
    f.write(html_report)
```

## 实盘交易

### 启动实盘交易

```python
from wtpy_quant.core.engine import WtpyEngine
from wtpy_quant.core.config_manager import load_config

# 加载配置
config = load_config()
config.engine.mode = "live"

# 创建引擎
engine = WtpyEngine(config.engine)

# 注册策略
strategy = ConceptStrategy(name="实盘策略")
strategy_id = engine.strategy_manager.register_strategy(strategy)

# 启动策略
engine.strategy_manager.start_strategy(strategy_id)

# 启动引擎
engine.start()

# 保持运行
try:
    while True:
        time.sleep(1)
except KeyboardInterrupt:
    engine.stop()
```

### 风险控制

系统提供多层级风险控制：

```python
from wtpy_quant.exec.risk_engine import RiskEngine, RiskRule

# 创建风险引擎
risk_engine = RiskEngine(config.risk)

# 添加自定义风险规则
def custom_risk_rule(order, portfolio, market_data):
    """自定义风险规则"""
    if portfolio.total_value > 10000000:  # 总资产超过1000万
        return "reject", "资产规模超限"
    return "allow", ""

risk_engine.add_rule("custom_rule", custom_risk_rule)
```

## API使用

### 启动API服务

```bash
# 开发模式
python run_api.py --reload --log-level DEBUG

# 生产模式
python run_api.py --host 0.0.0.0 --port 8000
```

### API调用示例

```python
import requests

# 获取策略列表
response = requests.get("http://localhost:8000/api/v1/strategy/")
strategies = response.json()

# 创建策略
strategy_data = {
    "name": "API创建的策略",
    "type": "ConceptStrategy",
    "parameters": {"max_positions": 10}
}
response = requests.post("http://localhost:8000/api/v1/strategy/", json=strategy_data)
strategy = response.json()

# 启动策略
control_data = {"action": "start"}
response = requests.post(f"http://localhost:8000/api/v1/strategy/{strategy['strategy_id']}/control", 
                        json=control_data)

# 获取实时数据 (WebSocket)
import websocket
import json

def on_message(ws, message):
    data = json.loads(message)
    print(f"收到数据: {data}")

ws = websocket.WebSocketApp("ws://localhost:8000/api/v1/monitoring/ws/realtime",
                           on_message=on_message)

# 订阅数据
subscribe_msg = {
    "type": "subscribe",
    "channels": ["system_status", "strategy_pnl"]
}
ws.send(json.dumps(subscribe_msg))
```

## 监控和风控

### 系统监控

```python
# 获取系统状态
response = requests.get("http://localhost:8000/api/v1/monitoring/system/status")
status = response.json()

# 获取策略监控
response = requests.get("http://localhost:8000/api/v1/monitoring/strategies/")
monitors = response.json()

# 获取风险指标
response = requests.get("http://localhost:8000/api/v1/monitoring/risk/metrics")
risk_metrics = response.json()
```

### 风险警报

```python
# 获取风险警报
response = requests.get("http://localhost:8000/api/v1/monitoring/risk/alerts")
alerts = response.json()

# 处理高风险警报
for alert in alerts['alerts']:
    if alert['severity'] == 'high':
        print(f"高风险警报: {alert['message']}")
```

## 常见问题

### Q: 如何处理数据缺失？

A: 系统会自动处理数据缺失情况，策略中可以检查数据完整性：

```python
def on_bar(self, context, stdCode, period, newBar):
    bars = self.get_bars(context, stdCode, period, 100)
    if bars is None or len(bars) < 50:
        self.log_warning(f"数据不足: {stdCode}")
        return
```

### Q: 如何优化策略性能？

A: 
1. 减少不必要的数据查询
2. 使用缓存机制
3. 优化指标计算
4. 合理设置订阅频率

### Q: 如何处理网络断线？

A: 系统具有自动重连机制，策略中可以监听连接状态：

```python
def on_channel_evt(self, context, trader, evtid):
    if evtid == 1:  # 连接断开
        self.log_warning("交易通道断开")
    elif evtid == 2:  # 重新连接
        self.log_info("交易通道恢复")
```

### Q: 如何备份和恢复数据？

A: 
1. 定期备份数据目录
2. 使用数据库存储重要数据
3. 实现数据同步机制

更多问题请参考项目Wiki或提交Issue。
