"""
Simple WebSocket broadcast manager for realtime UI updates.
"""
from __future__ import annotations
import asyncio
from typing import Set, Any

try:
    from starlette.websockets import WebSocket
except Exception:  # pragma: no cover
    WebSocket = object  # type: ignore


class WebSocketManager:
    def __init__(self) -> None:
        self._clients: Set[WebSocket] = set()
        self._lock = asyncio.Lock()

    async def connect(self, ws: WebSocket) -> None:
        await ws.accept()
        async with self._lock:
            self._clients.add(ws)

    async def disconnect(self, ws: WebSocket) -> None:
        async with self._lock:
            if ws in self._clients:
                self._clients.remove(ws)
        try:
            await ws.close()
        except Exception:
            pass

    async def broadcast(self, message: Any) -> None:
        # Send JSON to all clients; drop broken ones
        dead: list[WebSocket] = []
        async with self._lock:
            clients = list(self._clients)
        for ws in clients:
            try:
                await ws.send_json(message)
            except Exception:
                dead.append(ws)
        if dead:
            async with self._lock:
                for d in dead:
                    self._clients.discard(d)


# Global manager instance
manager = WebSocketManager()

