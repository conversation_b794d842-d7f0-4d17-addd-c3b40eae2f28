# GUI 前端界面

## 概述

本目录用于存放前端界面相关文件，支持以下技术栈：

- **Web界面**: React + Ant Design
- **桌面应用**: Electron 或 Tauri 封装
- **简单界面**: Streamlit 快速原型

## 目录结构（规划）

```
gui/
├── web/                # Web前端
│   ├── src/           # React源码
│   ├── public/        # 静态资源
│   └── package.json   # 依赖配置
├── desktop/           # 桌面应用
│   ├── electron/      # Electron配置
│   └── tauri/         # Tauri配置
└── streamlit/         # Streamlit应用
    ├── app.py         # 主应用
    └── pages/         # 页面组件
```

## 功能模块

### 策略管理
- 策略列表与状态监控
- 策略参数配置
- 策略启停控制

### 回测分析
- 回测任务管理
- 回测结果展示
- 性能指标分析

### 实时监控
- 实时行情展示
- 持仓与盈亏监控
- 风险指标监控

### 系统配置
- 系统参数配置
- 账户管理
- 日志查看

## 开发计划

1. **Phase 1**: Streamlit快速原型
2. **Phase 2**: React Web界面
3. **Phase 3**: 桌面应用封装

## 当前状态

🚧 **开发中** - 暂未实现，预留目录结构
