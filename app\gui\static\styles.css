* { box-sizing: border-box; }
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', 'PingFang SC', 'Microsoft YaHei', sans-serif; margin: 0; color: #222; }
header { padding: 12px 16px; background: #0e7; color: #fff; display: flex; align-items: center; gap: 12px; }
header h1 { margin: 0; font-size: 18px; }
main { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 16px; padding: 16px; }
section { border: 1px solid #eee; border-radius: 8px; padding: 12px; background: #fff; box-shadow: 0 1px 2px rgba(0,0,0,.04); }
label { display: block; margin: 6px 0; }
input { width: 100%; padding: 6px 8px; }
button { padding: 6px 10px; cursor: pointer; }
pre.log { background: #fafafa; border: 1px solid #eee; padding: 8px; border-radius: 6px; height: 160px; overflow: auto; }
.chart { height: 260px; border: 1px dashed #ccc; margin-top: 8px; }
footer { padding: 12px 16px; color: #666; border-top: 1px solid #eee; }

