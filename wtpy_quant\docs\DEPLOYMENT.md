# wtpy_quant 部署指南

本文档介绍如何在不同环境中部署wtpy_quant量化交易系统。

## 目录

1. [环境要求](#环境要求)
2. [开发环境部署](#开发环境部署)
3. [生产环境部署](#生产环境部署)
4. [Docker部署](#docker部署)
5. [云服务部署](#云服务部署)
6. [监控和维护](#监控和维护)
7. [故障排除](#故障排除)

## 环境要求

### 硬件要求

**最低配置:**
- CPU: 4核心
- 内存: 8GB
- 存储: 100GB SSD
- 网络: 稳定的互联网连接

**推荐配置:**
- CPU: 8核心以上
- 内存: 16GB以上
- 存储: 500GB SSD
- 网络: 低延迟专线

### 软件要求

- **操作系统**: Windows 10/11, Windows Server 2019/2022
- **Python**: 3.8 - 3.11
- **数据库**: PostgreSQL 12+ (可选)
- **Redis**: 6.0+ (可选，用于缓存)

### 网络要求

- **行情数据**: 需要访问券商行情服务器
- **交易接口**: 需要访问券商交易服务器
- **API服务**: 需要开放HTTP/WebSocket端口

## 开发环境部署

### 1. 环境准备

```bash
# 创建项目目录
mkdir wtpy_quant_dev
cd wtpy_quant_dev

# 克隆代码
git clone <repository-url> .

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件

```bash
# 复制配置模板
copy config\default.yaml config\config.yaml

# 编辑配置文件
notepad config\config.yaml
```

配置示例：
```yaml
engine:
  mode: "backtest"  # 开发环境使用回测模式
  data_path: "./dev_data"
  log_level: "DEBUG"

data:
  source: "mock"    # 使用模拟数据
  path: "./dev_data"

api:
  host: "127.0.0.1"
  port: 8000
```

### 3. 启动服务

```bash
# 启动API服务（开发模式）
python run_api.py --reload --log-level DEBUG

# 或者直接运行策略
python -m wtpy_quant.scripts.run_live
```

## 生产环境部署

### 1. 系统准备

```bash
# 创建专用用户
net user wtpy_quant /add
net localgroup "Users" wtpy_quant /add

# 创建应用目录
mkdir C:\wtpy_quant
cd C:\wtpy_quant

# 设置权限
icacls C:\wtpy_quant /grant wtpy_quant:(OI)(CI)F
```

### 2. 应用部署

```bash
# 部署代码
git clone <repository-url> .
git checkout production

# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt --no-cache-dir

# 安装生产依赖
pip install gunicorn supervisor
```

### 3. 配置文件

生产环境配置 `config/production.yaml`:

```yaml
engine:
  mode: "live"
  data_path: "C:/wtpy_quant/data"
  log_level: "INFO"

data:
  source: "xtdata"
  path: "C:/wtpy_quant/data"
  cache_size: 50000

trading:
  broker: "xttrader"
  accounts:
    - account_id: "${TRADING_ACCOUNT_ID}"
      account_type: "stock"

risk:
  max_position_ratio: 0.08
  max_drawdown: 0.15
  daily_loss_limit: 0.03

api:
  host: "0.0.0.0"
  port: 8000
  cors_origins: ["https://your-frontend.com"]

logging:
  level: "INFO"
  file: "C:/wtpy_quant/logs/app.log"
  max_size: "100MB"
  backup_count: 10
```

### 4. 环境变量

创建 `.env` 文件：

```bash
# 交易账户
TRADING_ACCOUNT_ID=your_account_id
TRADING_PASSWORD=your_password

# 数据源配置
XTDATA_USERNAME=your_username
XTDATA_PASSWORD=your_password

# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost/wtpy_quant

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_secret_key
API_TOKEN=your_api_token
```

### 5. Windows服务配置

创建Windows服务脚本 `install_service.py`:

```python
import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os

class WtpyQuantService(win32serviceutil.ServiceFramework):
    _svc_name_ = "WtpyQuantService"
    _svc_display_name_ = "wtpy_quant Trading System"
    _svc_description_ = "wtpy_quant量化交易系统服务"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                            servicemanager.PYS_SERVICE_STARTED,
                            (self._svc_name_, ''))
        self.main()

    def main(self):
        # 启动应用
        os.chdir('C:/wtpy_quant')
        os.system('venv/Scripts/python run_api.py --config config/production.yaml')

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(WtpyQuantService)
```

安装服务：
```bash
python install_service.py install
python install_service.py start
```

## Docker部署

### 1. Dockerfile

```dockerfile
FROM python:3.10-windowsservercore

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建数据目录
RUN mkdir data logs

# 暴露端口
EXPOSE 8000

# 设置环境变量
ENV PYTHONPATH=/app
ENV WTPY_QUANT_CONFIG=/app/config/docker.yaml

# 启动命令
CMD ["python", "run_api.py", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. docker-compose.yml

```yaml
version: '3.8'

services:
  wtpy-quant:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./config:/app/config
    environment:
      - WTPY_QUANT_CONFIG=/app/config/docker.yaml
      - TRADING_ACCOUNT_ID=${TRADING_ACCOUNT_ID}
      - TRADING_PASSWORD=${TRADING_PASSWORD}
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: wtpy_quant
      POSTGRES_USER: wtpy_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - wtpy-quant
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
```

### 3. 部署命令

```bash
# 构建和启动
docker-compose up -d

# 查看日志
docker-compose logs -f wtpy-quant

# 停止服务
docker-compose down
```

## 云服务部署

### Azure部署

1. **创建资源组**
```bash
az group create --name wtpy-quant-rg --location eastus
```

2. **创建虚拟机**
```bash
az vm create \
  --resource-group wtpy-quant-rg \
  --name wtpy-quant-vm \
  --image Win2022Datacenter \
  --admin-username azureuser \
  --admin-password YourPassword123! \
  --size Standard_D4s_v3
```

3. **配置网络安全组**
```bash
az network nsg rule create \
  --resource-group wtpy-quant-rg \
  --nsg-name wtpy-quant-vmNSG \
  --name AllowHTTP \
  --protocol tcp \
  --priority 1000 \
  --destination-port-range 8000
```

### AWS部署

使用AWS CloudFormation模板：

```yaml
AWSTemplateFormatVersion: '2010-09-09'
Resources:
  WtpyQuantInstance:
    Type: AWS::EC2::Instance
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Windows Server 2022
      InstanceType: m5.xlarge
      KeyName: !Ref KeyPairName
      SecurityGroupIds:
        - !Ref WtpyQuantSecurityGroup
      UserData:
        Fn::Base64: !Sub |
          <powershell>
          # 安装Python和Git
          choco install python git -y
          # 克隆代码并部署
          git clone https://github.com/your-repo/wtpy_quant.git C:\wtpy_quant
          cd C:\wtpy_quant
          python -m venv venv
          venv\Scripts\activate
          pip install -r requirements.txt
          </powershell>

  WtpyQuantSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for wtpy_quant
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 8000
          ToPort: 8000
          CidrIp: 0.0.0.0/0
        - IpProtocol: tcp
          FromPort: 3389
          ToPort: 3389
          CidrIp: 0.0.0.0/0
```

## 监控和维护

### 1. 日志监控

```bash
# 实时查看日志
tail -f logs/app.log

# 日志轮转配置
# 在logrotate中添加配置
```

### 2. 性能监控

```python
# 添加性能监控
import psutil
import time

def monitor_system():
    while True:
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        disk_percent = psutil.disk_usage('/').percent
        
        print(f"CPU: {cpu_percent}%, Memory: {memory_percent}%, Disk: {disk_percent}%")
        time.sleep(60)
```

### 3. 健康检查

```bash
# 创建健康检查脚本
@echo off
curl -f http://localhost:8000/health || exit 1
```

### 4. 备份策略

```bash
# 数据备份脚本
@echo off
set BACKUP_DIR=C:\backups\wtpy_quant_%date:~0,4%%date:~5,2%%date:~8,2%
mkdir %BACKUP_DIR%
xcopy C:\wtpy_quant\data %BACKUP_DIR%\data /E /I
xcopy C:\wtpy_quant\config %BACKUP_DIR%\config /E /I
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查端口占用: `netstat -an | findstr :8000`
   - 检查权限: 确保用户有读写权限
   - 检查配置文件: 验证YAML语法

2. **数据连接失败**
   - 检查网络连接
   - 验证账户凭据
   - 检查防火墙设置

3. **内存不足**
   - 调整缓存大小
   - 增加虚拟内存
   - 优化策略代码

4. **性能问题**
   - 检查CPU使用率
   - 优化数据库查询
   - 调整并发参数

### 调试工具

```bash
# 查看进程
tasklist | findstr python

# 查看端口
netstat -an | findstr :8000

# 查看日志
type logs\app.log | findstr ERROR

# 内存使用
wmic process where name="python.exe" get ProcessId,PageFileUsage
```

### 紧急恢复

```bash
# 停止所有服务
net stop WtpyQuantService

# 恢复备份
xcopy C:\backups\latest\* C:\wtpy_quant\ /E /Y

# 重启服务
net start WtpyQuantService
```

更多部署问题请参考项目Wiki或联系技术支持。
