# 核心依赖
wtpy>=0.9.0
numpy>=1.21.0
pandas>=1.3.0
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0
pyyaml>=6.0

# 数据存储
pyarrow>=5.0.0
h5py>=3.3.0

# 异步支持
aiofiles>=0.7.0
asyncio-mqtt>=0.10.0

# Web服务
websockets>=10.0
python-multipart>=0.0.5

# 系统监控
psutil>=5.8.0

# 模板引擎
jinja2>=3.0.0

# 图表和报告
matplotlib>=3.3.0
plotly>=5.0.0

# 网络请求
requests>=2.25.0
aiohttp>=3.7.0

# 数学计算
scipy>=1.7.0

# 日志与监控
loguru>=0.5.3
prometheus-client>=0.11.0

# 测试
pytest>=6.2.0
pytest-asyncio>=0.15.0
pytest-cov>=2.12.0
httpx>=0.24.0  # FastAPI测试客户端

# 开发工具
black>=21.6.0
flake8>=3.9.0
mypy>=0.910

# Web界面（可选）
streamlit>=1.0.0

# 数据库支持（可选）
sqlalchemy>=1.4.0
asyncpg>=0.24.0

# 机器学习（可选）
scikit-learn>=1.0.0
