#!/usr/bin/env python3
"""
回测运行脚本

运行wtpy_quant回测任务
"""

import sys
import os
import asyncio
from pathlib import Path
import argparse
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from wtpy_quant.core import ConfigManager
from wtpy_quant.backtest import BacktestEngine
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='运行wtpy_quant回测')
    
    parser.add_argument('--strategy', '-s', type=str, required=True,
                       help='策略名称')
    parser.add_argument('--start-date', type=str, 
                       default='20230101', help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end-date', type=str,
                       default='20231231', help='结束日期 (YYYYMMDD)')
    parser.add_argument('--capital', type=float,
                       default=1000000, help='初始资金')
    parser.add_argument('--config', '-c', type=str,
                       help='配置文件路径')
    parser.add_argument('--output', '-o', type=str,
                       help='结果输出目录')
    
    return parser.parse_args()


async def main():
    """主函数"""
    try:
        args = parse_args()
        
        logger.info(f"开始回测策略: {args.strategy}")
        logger.info(f"回测期间: {args.start_date} - {args.end_date}")
        logger.info(f"初始资金: {args.capital:,.0f}")
        
        # 加载配置
        config_manager = ConfigManager()
        if args.config:
            config = config_manager.load_config(args.config)
        else:
            config = config_manager.load_config()
        
        # 更新回测参数
        config.backtest.start_date = args.start_date
        config.backtest.end_date = args.end_date
        config.backtest.initial_capital = args.capital
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(config)
        
        # 运行回测
        result = await backtest_engine.run_backtest(
            strategy_name=args.strategy,
            output_dir=args.output
        )
        
        logger.info("回测完成！")
        logger.info(f"总收益率: {result.total_return:.2%}")
        logger.info(f"年化收益率: {result.annual_return:.2%}")
        logger.info(f"最大回撤: {result.max_drawdown:.2%}")
        logger.info(f"夏普比率: {result.sharpe_ratio:.2f}")
        
        if args.output:
            logger.info(f"详细报告已保存到: {args.output}")
            
    except Exception as e:
        logger.error(f"回测运行出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
