"""
配置管理器 - 系统配置加载与管理

负责加载和管理系统配置，支持YAML和JSON格式
"""

import os
import json
from pathlib import Path
from typing import Any, Dict, Optional, Union
from dataclasses import dataclass, field
import yaml
import logging

logger = logging.getLogger(__name__)


@dataclass
class SystemConfig:
    """系统配置"""
    name: str = "wtpy_quant"
    version: str = "1.0.0"
    log_level: str = "INFO"
    log_file: str = "logs/wtpy_quant.log"


@dataclass
class EngineConfig:
    """引擎配置"""
    mode: str = "live"  # live / backtest
    data_path: str = "data/"
    strategy_path: str = "strategies/"
    config_path: str = "config/"


@dataclass
class MmapConfig:
    """Mmap缓存配置"""
    enabled: bool = True
    path: str = "data/mmap/"
    days_to_keep: int = 7
    tick_buffer_size: int = 100000
    bar_buffer_size: int = 10000


@dataclass
class LongTermConfig:
    """长期存储配置"""
    type: str = "parquet"  # parquet / clickhouse / postgresql
    path: str = "data/parquet/"


@dataclass
class DataConfig:
    """数据配置"""
    market_source: str = "miniqmt"
    mmap: MmapConfig = field(default_factory=MmapConfig)
    longterm: LongTermConfig = field(default_factory=LongTermConfig)


@dataclass
class RiskConfig:
    """风控配置"""
    max_position_ratio: float = 0.95
    max_single_stock_ratio: float = 0.1
    max_daily_loss_ratio: float = 0.05
    max_orders_per_second: int = 10


@dataclass
class TradingConfig:
    """交易配置"""
    broker: str = "miniqmt"
    risk: RiskConfig = field(default_factory=RiskConfig)


@dataclass
class ApiConfig:
    """API服务配置"""
    host: str = "0.0.0.0"
    port: int = 8000
    reload: bool = False
    workers: int = 1


@dataclass
class CostConfig:
    """成本配置"""
    commission_rate: float = 0.0003
    stamp_tax_rate: float = 0.001
    slippage_rate: float = 0.0001


@dataclass
class BacktestConfig:
    """回测配置"""
    start_date: str = "20230101"
    end_date: str = "20231231"
    initial_capital: float = 1000000
    costs: CostConfig = field(default_factory=CostConfig)


@dataclass
class Config:
    """主配置类"""
    system: SystemConfig = field(default_factory=SystemConfig)
    engine: EngineConfig = field(default_factory=EngineConfig)
    data: DataConfig = field(default_factory=DataConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    api: ApiConfig = field(default_factory=ApiConfig)
    backtest: BacktestConfig = field(default_factory=BacktestConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录下的config
        """
        if config_dir is None:
            config_dir = Path(__file__).parent.parent / "config"
        self.config_dir = Path(config_dir)
        self._config: Optional[Config] = None
        
    def load_config(self, config_file: Optional[Union[str, Path]] = None) -> Config:
        """
        加载配置文件
        
        Args:
            config_file: 配置文件路径，默认为config.yaml或default.yaml
            
        Returns:
            Config: 配置对象
        """
        if config_file is None:
            # 优先查找config.yaml，然后是default.yaml
            for filename in ["config.yaml", "default.yaml"]:
                config_path = self.config_dir / filename
                if config_path.exists():
                    config_file = config_path
                    break
            else:
                raise FileNotFoundError(f"No config file found in {self.config_dir}")
        
        config_path = Path(config_file)
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
            
        logger.info(f"Loading config from: {config_path}")
        
        # 根据文件扩展名选择解析器
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f)
        elif config_path.suffix.lower() == '.json':
            with open(config_path, 'r', encoding='utf-8') as f:
                config_dict = json.load(f)
        else:
            raise ValueError(f"Unsupported config file format: {config_path.suffix}")
        
        # 转换为配置对象
        self._config = self._dict_to_config(config_dict)
        
        # 设置日志级别
        logging.getLogger().setLevel(self._config.system.log_level)
        
        return self._config
    
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> Config:
        """将字典转换为配置对象"""
        config = Config()
        
        # 系统配置
        if 'system' in config_dict:
            system_dict = config_dict['system']
            config.system = SystemConfig(
                name=system_dict.get('name', config.system.name),
                version=system_dict.get('version', config.system.version),
                log_level=system_dict.get('log_level', config.system.log_level),
                log_file=system_dict.get('log_file', config.system.log_file)
            )
        
        # 引擎配置
        if 'engine' in config_dict:
            engine_dict = config_dict['engine']
            config.engine = EngineConfig(
                mode=engine_dict.get('mode', config.engine.mode),
                data_path=engine_dict.get('data_path', config.engine.data_path),
                strategy_path=engine_dict.get('strategy_path', config.engine.strategy_path),
                config_path=engine_dict.get('config_path', config.engine.config_path)
            )
        
        # 数据配置
        if 'data' in config_dict:
            data_dict = config_dict['data']
            
            # Mmap配置
            mmap_config = MmapConfig()
            if 'mmap' in data_dict:
                mmap_dict = data_dict['mmap']
                mmap_config = MmapConfig(
                    enabled=mmap_dict.get('enabled', mmap_config.enabled),
                    path=mmap_dict.get('path', mmap_config.path),
                    days_to_keep=mmap_dict.get('days_to_keep', mmap_config.days_to_keep),
                    tick_buffer_size=mmap_dict.get('tick_buffer_size', mmap_config.tick_buffer_size),
                    bar_buffer_size=mmap_dict.get('bar_buffer_size', mmap_config.bar_buffer_size)
                )
            
            # 长期存储配置
            longterm_config = LongTermConfig()
            if 'longterm' in data_dict:
                longterm_dict = data_dict['longterm']
                longterm_config = LongTermConfig(
                    type=longterm_dict.get('type', longterm_config.type),
                    path=longterm_dict.get('path', longterm_config.path)
                )
            
            config.data = DataConfig(
                market_source=data_dict.get('market_source', config.data.market_source),
                mmap=mmap_config,
                longterm=longterm_config
            )
        
        # 交易配置
        if 'trading' in config_dict:
            trading_dict = config_dict['trading']
            
            # 风控配置
            risk_config = RiskConfig()
            if 'risk' in trading_dict:
                risk_dict = trading_dict['risk']
                risk_config = RiskConfig(
                    max_position_ratio=risk_dict.get('max_position_ratio', risk_config.max_position_ratio),
                    max_single_stock_ratio=risk_dict.get('max_single_stock_ratio', risk_config.max_single_stock_ratio),
                    max_daily_loss_ratio=risk_dict.get('max_daily_loss_ratio', risk_config.max_daily_loss_ratio),
                    max_orders_per_second=risk_dict.get('max_orders_per_second', risk_config.max_orders_per_second)
                )
            
            config.trading = TradingConfig(
                broker=trading_dict.get('broker', config.trading.broker),
                risk=risk_config
            )
        
        # API配置
        if 'api' in config_dict:
            api_dict = config_dict['api']
            config.api = ApiConfig(
                host=api_dict.get('host', config.api.host),
                port=api_dict.get('port', config.api.port),
                reload=api_dict.get('reload', config.api.reload),
                workers=api_dict.get('workers', config.api.workers)
            )
        
        # 回测配置
        if 'backtest' in config_dict:
            backtest_dict = config_dict['backtest']
            
            # 成本配置
            cost_config = CostConfig()
            if 'costs' in backtest_dict:
                costs_dict = backtest_dict['costs']
                cost_config = CostConfig(
                    commission_rate=costs_dict.get('commission_rate', cost_config.commission_rate),
                    stamp_tax_rate=costs_dict.get('stamp_tax_rate', cost_config.stamp_tax_rate),
                    slippage_rate=costs_dict.get('slippage_rate', cost_config.slippage_rate)
                )
            
            config.backtest = BacktestConfig(
                start_date=backtest_dict.get('start_date', config.backtest.start_date),
                end_date=backtest_dict.get('end_date', config.backtest.end_date),
                initial_capital=backtest_dict.get('initial_capital', config.backtest.initial_capital),
                costs=cost_config
            )
        
        return config
    
    def get_config(self) -> Optional[Config]:
        """获取当前配置"""
        return self._config
    
    def save_config(self, config: Config, config_file: Optional[Union[str, Path]] = None) -> None:
        """
        保存配置到文件
        
        Args:
            config: 配置对象
            config_file: 配置文件路径，默认为config.yaml
        """
        if config_file is None:
            config_file = self.config_dir / "config.yaml"
        
        config_path = Path(config_file)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 转换为字典
        config_dict = self._config_to_dict(config)
        
        # 保存为YAML格式
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"Config saved to: {config_path}")
    
    def _config_to_dict(self, config: Config) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        return {
            'system': {
                'name': config.system.name,
                'version': config.system.version,
                'log_level': config.system.log_level,
                'log_file': config.system.log_file
            },
            'engine': {
                'mode': config.engine.mode,
                'data_path': config.engine.data_path,
                'strategy_path': config.engine.strategy_path,
                'config_path': config.engine.config_path
            },
            'data': {
                'market_source': config.data.market_source,
                'mmap': {
                    'enabled': config.data.mmap.enabled,
                    'path': config.data.mmap.path,
                    'days_to_keep': config.data.mmap.days_to_keep,
                    'tick_buffer_size': config.data.mmap.tick_buffer_size,
                    'bar_buffer_size': config.data.mmap.bar_buffer_size
                },
                'longterm': {
                    'type': config.data.longterm.type,
                    'path': config.data.longterm.path
                }
            },
            'trading': {
                'broker': config.trading.broker,
                'risk': {
                    'max_position_ratio': config.trading.risk.max_position_ratio,
                    'max_single_stock_ratio': config.trading.risk.max_single_stock_ratio,
                    'max_daily_loss_ratio': config.trading.risk.max_daily_loss_ratio,
                    'max_orders_per_second': config.trading.risk.max_orders_per_second
                }
            },
            'api': {
                'host': config.api.host,
                'port': config.api.port,
                'reload': config.api.reload,
                'workers': config.api.workers
            },
            'backtest': {
                'start_date': config.backtest.start_date,
                'end_date': config.backtest.end_date,
                'initial_capital': config.backtest.initial_capital,
                'costs': {
                    'commission_rate': config.backtest.costs.commission_rate,
                    'stamp_tax_rate': config.backtest.costs.stamp_tax_rate,
                    'slippage_rate': config.backtest.costs.slippage_rate
                }
            }
        }
