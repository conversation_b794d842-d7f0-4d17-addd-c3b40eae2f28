"""
pytest配置文件

提供测试用的fixtures和配置
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import MagicMock

from wtpy_quant.core import ConfigManager


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_config():
    """模拟配置对象"""
    config = MagicMock()
    config.system.name = "wtpy_quant_test"
    config.system.log_level = "DEBUG"
    config.data.mmap.enabled = True
    config.data.mmap.path = "test_data/mmap/"
    config.trading.risk.max_position_ratio = 0.95
    return config


@pytest.fixture
def config_manager():
    """配置管理器"""
    return ConfigManager()


@pytest.fixture
def sample_tick_data():
    """样例Tick数据"""
    return {
        'symbol': '000001.SZ',
        'datetime': '20231201 09:30:00',
        'price': 10.50,
        'volume': 1000,
        'turnover': 10500,
        'bid_price': 10.49,
        'ask_price': 10.51,
        'bid_volume': 500,
        'ask_volume': 800
    }


@pytest.fixture
def sample_bar_data():
    """样例K线数据"""
    return {
        'symbol': '000001.SZ',
        'datetime': '20231201 09:30:00',
        'open': 10.45,
        'high': 10.55,
        'low': 10.40,
        'close': 10.50,
        'volume': 100000,
        'turnover': 1050000
    }
