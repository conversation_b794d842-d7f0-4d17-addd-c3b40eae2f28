"""
核心框架测试
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

from wtpy_quant.core import ConfigManager, EventBridge, StrategyManager, WtpyEngine
from wtpy_quant.core.config_manager import Config
from wtpy_quant.core.event_bridge import EventType, Event


class TestConfigManager:
    """配置管理器测试"""
    
    def test_load_default_config(self, temp_dir):
        """测试加载默认配置"""
        # 创建测试配置文件
        config_dir = temp_dir / "config"
        config_dir.mkdir()
        
        config_content = """
system:
  name: "test_system"
  log_level: "DEBUG"
engine:
  mode: "backtest"
data:
  market_source: "test"
"""
        
        config_file = config_dir / "default.yaml"
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        # 测试加载配置
        config_manager = ConfigManager(config_dir)
        config = config_manager.load_config()
        
        assert config.system.name == "test_system"
        assert config.system.log_level == "DEBUG"
        assert config.engine.mode == "backtest"
        assert config.data.market_source == "test"
        
    def test_save_config(self, temp_dir):
        """测试保存配置"""
        config_dir = temp_dir / "config"
        config_manager = ConfigManager(config_dir)
        
        # 创建配置对象
        config = Config()
        config.system.name = "test_save"
        
        # 保存配置
        config_manager.save_config(config)
        
        # 验证文件存在
        config_file = config_dir / "config.yaml"
        assert config_file.exists()
        
        # 重新加载验证
        loaded_config = config_manager.load_config(config_file)
        assert loaded_config.system.name == "test_save"


class TestEventBridge:
    """事件桥接器测试"""
    
    @pytest.mark.asyncio
    async def test_event_emission_and_handling(self):
        """测试事件发送和处理"""
        bridge = EventBridge()
        
        # 创建测试处理器
        handled_events = []
        
        async def test_handler(event):
            handled_events.append(event)
        
        # 注册处理器
        bridge.register_async_handler(EventType.TICK, test_handler)
        
        # 启动桥接器
        await bridge.start()
        
        # 发送事件
        test_event = Event(
            type=EventType.TICK,
            data={"symbol": "000001.SZ", "price": 10.0},
            source="test"
        )
        
        await bridge.emit(test_event)
        
        # 等待处理完成
        await asyncio.sleep(0.1)
        
        # 验证事件被处理
        assert len(handled_events) == 1
        assert handled_events[0].type == EventType.TICK
        assert handled_events[0].data["symbol"] == "000001.SZ"
        
        # 停止桥接器
        await bridge.stop()
        
    @pytest.mark.asyncio
    async def test_multiple_handlers(self):
        """测试多个处理器"""
        bridge = EventBridge()
        
        handler1_events = []
        handler2_events = []
        
        async def handler1(event):
            handler1_events.append(event)
            
        async def handler2(event):
            handler2_events.append(event)
        
        # 注册多个处理器
        bridge.register_async_handler(EventType.TICK, handler1)
        bridge.register_async_handler(EventType.TICK, handler2)
        
        await bridge.start()
        
        # 发送事件
        test_event = Event(type=EventType.TICK, data={}, source="test")
        await bridge.emit(test_event)
        
        await asyncio.sleep(0.1)
        
        # 验证所有处理器都被调用
        assert len(handler1_events) == 1
        assert len(handler2_events) == 1
        
        await bridge.stop()


class TestStrategyManager:
    """策略管理器测试"""
    
    @pytest.mark.asyncio
    async def test_strategy_registration(self):
        """测试策略注册"""
        bridge = EventBridge()
        await bridge.start()
        
        manager = StrategyManager(bridge)
        
        # 注册策略
        success = manager.register_strategy(
            name="test_strategy",
            module_path="test.module",
            class_name="TestStrategy",
            description="Test strategy"
        )
        
        assert success
        
        # 验证策略信息
        strategy_info = manager.get_strategy_info("test_strategy")
        assert strategy_info is not None
        assert strategy_info.name == "test_strategy"
        assert strategy_info.module_path == "test.module"
        assert strategy_info.class_name == "TestStrategy"
        
        # 验证策略列表
        strategies = manager.list_strategies()
        assert len(strategies) == 1
        assert strategies[0].name == "test_strategy"
        
        await bridge.stop()
        
    @pytest.mark.asyncio
    async def test_strategy_params_update(self):
        """测试策略参数更新"""
        bridge = EventBridge()
        await bridge.start()
        
        manager = StrategyManager(bridge)
        
        # 注册策略
        manager.register_strategy(
            name="test_strategy",
            module_path="test.module",
            class_name="TestStrategy",
            params={"param1": "value1"}
        )
        
        # 更新参数
        success = manager.update_strategy_params(
            "test_strategy",
            {"param1": "new_value", "param2": "value2"}
        )
        
        assert success
        
        # 验证参数更新
        strategy_info = manager.get_strategy_info("test_strategy")
        assert strategy_info.params["param1"] == "new_value"
        assert strategy_info.params["param2"] == "value2"
        
        await bridge.stop()


class TestWtpyEngine:
    """wtpy引擎测试"""
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self, mock_config):
        """测试引擎初始化"""
        # Mock wtpy模块
        with patch('wtpy_quant.core.engine.WtpyEngine._check_wtpy_available', return_value=False):
            engine = WtpyEngine(mock_config)
            
            # 测试初始化失败（wtpy不可用）
            success = await engine.initialize()
            assert not success
            
    @pytest.mark.asyncio
    async def test_engine_config_generation(self, mock_config, temp_dir):
        """测试引擎配置生成"""
        # 设置临时配置目录
        mock_config.engine.config_path = str(temp_dir / "config")
        
        engine = WtpyEngine(mock_config)
        
        # 生成配置文件
        config_path = await engine._generate_wtpy_config()
        
        # 验证配置文件存在
        assert config_path.exists()
        
        # 验证配置内容
        import json
        with open(config_path) as f:
            config_data = json.load(f)
            
        assert config_data["version"] == "0.1.0"
        assert config_data["data"]["source"] == mock_config.data.market_source
        assert len(config_data["accounts"]) == 1
        assert config_data["accounts"][0]["broker"] == mock_config.trading.broker
        
    def test_engine_stats(self, mock_config):
        """测试引擎统计信息"""
        engine = WtpyEngine(mock_config)
        
        stats = engine.get_stats()
        
        assert "start_time" in stats
        assert "uptime" in stats
        assert "event_bridge" in stats
        assert "strategy_manager" in stats
        
    def test_engine_mode(self, mock_config):
        """测试引擎模式"""
        engine = WtpyEngine(mock_config)
        
        assert engine.get_mode() == mock_config.engine.mode
        assert not engine.is_running()


if __name__ == "__main__":
    pytest.main([__file__])
