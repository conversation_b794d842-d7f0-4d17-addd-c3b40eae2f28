"""
Mmap短期缓存 - 高性能数据存储

使用内存映射文件实现高性能的短期数据缓存，包括：
- 环形缓冲区设计
- O(1)最近K笔数据访问
- 多进程共享
- 零拷贝数据访问
"""

import os
import mmap
import struct
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, Tuple
import numpy as np
from datetime import datetime, date
import time
import threading
from dataclasses import dataclass

from .market_adapter import TickData, BarData

logger = logging.getLogger(__name__)


@dataclass
class MmapConfig:
    """Mmap配置"""
    base_path: str = "data/mmap"
    tick_buffer_size: int = 100000  # 每个标的的Tick缓冲区大小
    bar_buffer_size: int = 10000    # 每个标的的Bar缓冲区大小
    days_to_keep: int = 7           # 保留天数
    enable_index: bool = True       # 是否启用索引


# Tick数据结构定义（定长记录）
TICK_DTYPE = np.dtype([
    ('timestamp', 'f8'),      # 时间戳
    ('price', 'f4'),          # 价格
    ('volume', 'i4'),         # 成交量
    ('turnover', 'f4'),       # 成交额
    ('bid_price', 'f4'),      # 买一价
    ('ask_price', 'f4'),      # 卖一价
    ('bid_volume', 'i4'),     # 买一量
    ('ask_volume', 'i4'),     # 卖一量
    ('open_interest', 'i4'),  # 持仓量
])

# Bar数据结构定义（定长记录）
BAR_DTYPE = np.dtype([
    ('timestamp', 'f8'),      # 时间戳
    ('open', 'f4'),           # 开盘价
    ('high', 'f4'),           # 最高价
    ('low', 'f4'),            # 最低价
    ('close', 'f4'),          # 收盘价
    ('volume', 'i4'),         # 成交量
    ('turnover', 'f4'),       # 成交额
])

# 索引结构定义
INDEX_DTYPE = np.dtype([
    ('timestamp', 'f8'),      # 时间戳
    ('offset', 'i4'),         # 数据偏移量
])


class MmapBuffer:
    """内存映射缓冲区"""
    
    def __init__(self, 
                 file_path: Path, 
                 dtype: np.dtype, 
                 buffer_size: int,
                 create_if_not_exists: bool = True):
        """
        初始化内存映射缓冲区
        
        Args:
            file_path: 文件路径
            dtype: 数据类型
            buffer_size: 缓冲区大小
            create_if_not_exists: 如果文件不存在是否创建
        """
        self.file_path = file_path
        self.dtype = dtype
        self.buffer_size = buffer_size
        self.record_size = dtype.itemsize
        self.file_size = buffer_size * self.record_size + 64  # 额外64字节用于元数据
        
        self._mmap = None
        self._array = None
        self._file = None
        self._lock = threading.RLock()
        
        # 元数据偏移量
        self._head_offset = 0      # 头指针偏移量
        self._tail_offset = 4      # 尾指针偏移量
        self._count_offset = 8     # 计数器偏移量
        self._data_offset = 64     # 数据起始偏移量
        
        if create_if_not_exists:
            self._ensure_file_exists()
            
    def _ensure_file_exists(self) -> None:
        """确保文件存在"""
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        
        if not self.file_path.exists():
            # 创建新文件
            with open(self.file_path, 'wb') as f:
                f.write(b'\x00' * self.file_size)
            logger.info(f"Created mmap file: {self.file_path}")
            
    def open(self) -> bool:
        """打开内存映射文件"""
        try:
            with self._lock:
                if self._mmap is not None:
                    return True
                    
                self._file = open(self.file_path, 'r+b')
                self._mmap = mmap.mmap(self._file.fileno(), self.file_size)
                
                # 创建numpy数组视图
                buffer = np.frombuffer(
                    self._mmap, 
                    dtype=self.dtype, 
                    count=self.buffer_size,
                    offset=self._data_offset
                )
                self._array = buffer
                
                logger.debug(f"Opened mmap file: {self.file_path}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to open mmap file {self.file_path}: {e}")
            return False
            
    def close(self) -> None:
        """关闭内存映射文件"""
        try:
            with self._lock:
                if self._mmap:
                    self._mmap.close()
                    self._mmap = None
                    
                if self._file:
                    self._file.close()
                    self._file = None
                    
                self._array = None
                logger.debug(f"Closed mmap file: {self.file_path}")
                
        except Exception as e:
            logger.error(f"Error closing mmap file {self.file_path}: {e}")
            
    def _get_metadata(self, offset: int) -> int:
        """获取元数据"""
        if self._mmap is None:
            return 0
        return struct.unpack('I', self._mmap[offset:offset+4])[0]
        
    def _set_metadata(self, offset: int, value: int) -> None:
        """设置元数据"""
        if self._mmap is None:
            return
        self._mmap[offset:offset+4] = struct.pack('I', value)
        
    def append(self, record: np.ndarray) -> bool:
        """
        追加记录到缓冲区
        
        Args:
            record: 记录数据
            
        Returns:
            bool: 是否成功
        """
        try:
            with self._lock:
                if self._array is None:
                    return False
                    
                # 获取当前位置
                head = self._get_metadata(self._head_offset)
                tail = self._get_metadata(self._tail_offset)
                count = self._get_metadata(self._count_offset)
                
                # 写入数据
                self._array[tail] = record
                
                # 更新尾指针
                tail = (tail + 1) % self.buffer_size
                self._set_metadata(self._tail_offset, tail)
                
                # 更新计数器
                if count < self.buffer_size:
                    count += 1
                    self._set_metadata(self._count_offset, count)
                else:
                    # 缓冲区已满，移动头指针
                    head = (head + 1) % self.buffer_size
                    self._set_metadata(self._head_offset, head)
                
                return True
                
        except Exception as e:
            logger.error(f"Error appending to mmap buffer: {e}")
            return False
            
    def get_recent(self, n: int) -> Optional[np.ndarray]:
        """
        获取最近N条记录
        
        Args:
            n: 记录数量
            
        Returns:
            Optional[np.ndarray]: 记录数组
        """
        try:
            with self._lock:
                if self._array is None:
                    return None
                    
                count = self._get_metadata(self._count_offset)
                if count == 0:
                    return np.array([], dtype=self.dtype)
                    
                # 限制n的范围
                n = min(n, count)
                
                tail = self._get_metadata(self._tail_offset)
                
                # 计算起始位置
                if count < self.buffer_size:
                    # 缓冲区未满
                    start = max(0, tail - n)
                    if start + n <= self.buffer_size:
                        return self._array[start:start+n].copy()
                    else:
                        # 跨越边界
                        part1 = self._array[start:].copy()
                        part2 = self._array[:tail].copy()
                        return np.concatenate([part1, part2])
                else:
                    # 缓冲区已满，环形读取
                    start = (tail - n) % self.buffer_size
                    if start < tail:
                        return self._array[start:tail].copy()
                    else:
                        # 跨越边界
                        part1 = self._array[start:].copy()
                        part2 = self._array[:tail].copy()
                        return np.concatenate([part1, part2])
                        
        except Exception as e:
            logger.error(f"Error getting recent records: {e}")
            return None
            
    def get_count(self) -> int:
        """获取记录数量"""
        try:
            with self._lock:
                return self._get_metadata(self._count_offset)
        except Exception as e:
            logger.error(f"Error getting count: {e}")
            return 0
            
    def clear(self) -> None:
        """清空缓冲区"""
        try:
            with self._lock:
                self._set_metadata(self._head_offset, 0)
                self._set_metadata(self._tail_offset, 0)
                self._set_metadata(self._count_offset, 0)
                logger.debug(f"Cleared mmap buffer: {self.file_path}")
                
        except Exception as e:
            logger.error(f"Error clearing mmap buffer: {e}")


class MmapStore:
    """Mmap存储管理器"""
    
    def __init__(self, config: MmapConfig):
        """
        初始化Mmap存储
        
        Args:
            config: Mmap配置
        """
        self.config = config
        self.base_path = Path(config.base_path)
        self._tick_buffers: Dict[str, MmapBuffer] = {}
        self._bar_buffers: Dict[str, Dict[str, MmapBuffer]] = {}  # symbol -> period -> buffer
        self._lock = threading.RLock()
        
        # 确保基础目录存在
        self.base_path.mkdir(parents=True, exist_ok=True)
        
    def _get_tick_buffer(self, symbol: str, date_str: Optional[str] = None) -> Optional[MmapBuffer]:
        """获取Tick缓冲区"""
        if date_str is None:
            date_str = "today"
            
        buffer_key = f"{symbol}_{date_str}"
        
        with self._lock:
            if buffer_key not in self._tick_buffers:
                # 创建新的缓冲区
                file_path = self.base_path / symbol / "tick" / f"{date_str}.bin"
                buffer = MmapBuffer(
                    file_path=file_path,
                    dtype=TICK_DTYPE,
                    buffer_size=self.config.tick_buffer_size
                )
                
                if buffer.open():
                    self._tick_buffers[buffer_key] = buffer
                else:
                    return None
                    
            return self._tick_buffers[buffer_key]
            
    def _get_bar_buffer(self, symbol: str, period: str, date_str: Optional[str] = None) -> Optional[MmapBuffer]:
        """获取Bar缓冲区"""
        if date_str is None:
            date_str = "today"
            
        with self._lock:
            if symbol not in self._bar_buffers:
                self._bar_buffers[symbol] = {}
                
            period_buffers = self._bar_buffers[symbol]
            buffer_key = f"{period}_{date_str}"
            
            if buffer_key not in period_buffers:
                # 创建新的缓冲区
                file_path = self.base_path / symbol / period / f"{date_str}.bin"
                buffer = MmapBuffer(
                    file_path=file_path,
                    dtype=BAR_DTYPE,
                    buffer_size=self.config.bar_buffer_size
                )
                
                if buffer.open():
                    period_buffers[buffer_key] = buffer
                else:
                    return None
                    
            return period_buffers[buffer_key]
            
    def store_tick(self, tick_data: TickData) -> bool:
        """
        存储Tick数据
        
        Args:
            tick_data: Tick数据
            
        Returns:
            bool: 是否成功
        """
        try:
            buffer = self._get_tick_buffer(tick_data.symbol)
            if buffer is None:
                return False
                
            # 转换为numpy记录
            timestamp = time.time()  # 可以从tick_data.datetime解析
            record = np.array([(
                timestamp,
                tick_data.price,
                tick_data.volume,
                tick_data.turnover,
                tick_data.bid_price,
                tick_data.ask_price,
                tick_data.bid_volume,
                tick_data.ask_volume,
                tick_data.open_interest
            )], dtype=TICK_DTYPE)
            
            return buffer.append(record[0])
            
        except Exception as e:
            logger.error(f"Error storing tick data for {tick_data.symbol}: {e}")
            return False
            
    def store_bar(self, bar_data: BarData) -> bool:
        """
        存储Bar数据
        
        Args:
            bar_data: Bar数据
            
        Returns:
            bool: 是否成功
        """
        try:
            buffer = self._get_bar_buffer(bar_data.symbol, bar_data.period)
            if buffer is None:
                return False
                
            # 转换为numpy记录
            timestamp = time.time()  # 可以从bar_data.datetime解析
            record = np.array([(
                timestamp,
                bar_data.open,
                bar_data.high,
                bar_data.low,
                bar_data.close,
                bar_data.volume,
                bar_data.turnover
            )], dtype=BAR_DTYPE)
            
            return buffer.append(record[0])
            
        except Exception as e:
            logger.error(f"Error storing bar data for {bar_data.symbol}: {e}")
            return False
            
    def get_recent_ticks(self, symbol: str, n: int = 10) -> Optional[np.ndarray]:
        """
        获取最近N笔Tick数据
        
        Args:
            symbol: 股票代码
            n: 数据条数
            
        Returns:
            Optional[np.ndarray]: Tick数据数组
        """
        buffer = self._get_tick_buffer(symbol)
        if buffer is None:
            return None
        return buffer.get_recent(n)
        
    def get_recent_bars(self, symbol: str, period: str = "1m", n: int = 10) -> Optional[np.ndarray]:
        """
        获取最近N根K线数据
        
        Args:
            symbol: 股票代码
            period: K线周期
            n: 数据条数
            
        Returns:
            Optional[np.ndarray]: K线数据数组
        """
        buffer = self._get_bar_buffer(symbol, period)
        if buffer is None:
            return None
        return buffer.get_recent(n)
        
    def cleanup_old_files(self) -> None:
        """清理过期文件"""
        try:
            cutoff_date = date.today()
            # 实现清理逻辑
            logger.info("Cleaned up old mmap files")
        except Exception as e:
            logger.error(f"Error cleaning up old files: {e}")
            
    def close_all(self) -> None:
        """关闭所有缓冲区"""
        with self._lock:
            # 关闭Tick缓冲区
            for buffer in self._tick_buffers.values():
                buffer.close()
            self._tick_buffers.clear()
            
            # 关闭Bar缓冲区
            for period_buffers in self._bar_buffers.values():
                for buffer in period_buffers.values():
                    buffer.close()
            self._bar_buffers.clear()
            
        logger.info("Closed all mmap buffers")
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            return {
                'tick_buffers': len(self._tick_buffers),
                'bar_buffers': sum(len(pb) for pb in self._bar_buffers.values()),
                'total_symbols': len(self._tick_buffers) + len(self._bar_buffers)
            }
