"""
wtpy_quant - 基于wtpy/wondertrader的A股量化交易系统

本系统实现了一个完整的量化交易平台，包括：
- 实时行情数据接入（miniqmt/xtdata）
- 高性能数据存储（mmap短期缓存 + 长期存储）
- 策略框架与样例策略
- 交易执行与风险控制
- 回测引擎集成
- Web API服务

主要特性：
- 直接使用wtpy与wondertrader的高性能C++核心
- 支持全市场A股选股与交易
- 概念板块因子与微结构因子策略
- 实盘与回测统一的策略接口
"""

__version__ = "1.0.0"
__author__ = "wtpy_quant"

# 导入主要模块
from . import core
from . import data
from . import exec
from . import strategy
from . import backtest
from . import api

__all__ = [
    "core",
    "data", 
    "exec",
    "strategy",
    "backtest",
    "api"
]
