<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>WTpy/WonderTrader 控制台</title>
    <link rel="stylesheet" href="/ui/styles.css" />
  </head>
  <body>
    <header>
      <h1>WTpy/WonderTrader 控制台</h1>
      <button id="btnPing">健康检查</button>
      <span id="pingStatus"></span>
    </header>

    <main>
      <section>
        <h2>回测</h2>
        <label>策略(模块/文件:类)：<input id="btStrategy" placeholder="app.strategy.demo_cta:DemoCtaStrategy" /></label>
        <label>配置路径：<input id="btConfig" value="app/config/engine.config.json" /></label>
        <label>输出目录：<input id="btOut" value="runs/backtest" /></label>
        <button id="btnRunBt">运行回测</button>
        <pre id="btResult" class="log"></pre>
        <div id="btChart" class="chart"></div>
      </section>

      <section>
        <h2>实盘</h2>
        <label>配置路径：<input id="liveConfig" value="app/config/engine.config.json" /></label>
        <button id="btnRunLive">启动实盘</button>
        <pre id="liveResult" class="log"></pre>
      </section>

      <section>
        <h2>策略管理</h2>
        <button id="btnListStrategies">列出策略</button>
        <div>
          <label>名称：<input id="stName" value="demo_cta" /></label>
          <label>描述：<input id="stDesc" placeholder="演示策略" /></label>
          <label>启用：<input id="stEnabled" type="checkbox" checked /></label>
          <button id="btnAddStrategy">注册策略</button>
        </div>
        <pre id="stResult" class="log"></pre>
      </section>

      <section>
        <h2>账户</h2>
        <button id="btnListAccounts">查询账户</button>
        <pre id="acctResult" class="log"></pre>
      </section>

      <section>
        <h2>数据</h2>
        <div>
          <label>Symbol：<input id="dqSymbol" value="SH600000" /></label>
          <button id="btnTick">获取 Tick</button>
          <button id="btnBar">获取 Bar(1m)</button>
        </div>
        <pre id="dataResult" class="log"></pre>
      </section>
    </main>

    <footer>
      <small>Powered by wtpy/wondertrader C++ 内核 + FastAPI</small>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/echarts@5/dist/echarts.min.js"></script>
    <script src="/ui/app.js"></script>
  </body>
</html>

