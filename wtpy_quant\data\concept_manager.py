"""
概念管理器 - 概念板块与成份股管理

负责概念板块数据的管理和分析，包括：
- 概念板块与成份股映射
- 概念板块实时涨跌幅计算
- 成份股贡献度分析
- 概念轮动分析
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime, date
import pandas as pd
import numpy as np
from collections import defaultdict
import json

logger = logging.getLogger(__name__)


@dataclass
class ConceptInfo:
    """概念信息"""
    code: str
    name: str
    description: str = ""
    category: str = ""
    created_date: str = ""
    
    
@dataclass
class ConceptPerformance:
    """概念表现"""
    concept_code: str
    date: str
    open_price: float = 0.0
    close_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    change_pct: float = 0.0
    volume: int = 0
    turnover: float = 0.0
    market_cap: float = 0.0
    constituent_count: int = 0
    up_count: int = 0
    down_count: int = 0
    flat_count: int = 0
    
    
@dataclass
class ConceptConstituent:
    """概念成份股"""
    concept_code: str
    symbol: str
    weight: float = 0.0  # 权重
    join_date: str = ""
    status: str = "active"  # active, inactive


class ConceptManager:
    """概念管理器"""
    
    def __init__(self, data_source: str = "local"):
        """
        初始化概念管理器
        
        Args:
            data_source: 数据源类型 (local, api, database)
        """
        self.data_source = data_source
        
        # 概念信息
        self._concepts: Dict[str, ConceptInfo] = {}
        
        # 概念成份股映射 concept_code -> [symbols]
        self._concept_constituents: Dict[str, List[ConceptConstituent]] = defaultdict(list)
        
        # 股票概念映射 symbol -> [concept_codes]
        self._symbol_concepts: Dict[str, List[str]] = defaultdict(list)
        
        # 概念表现数据
        self._concept_performance: Dict[str, Dict[str, ConceptPerformance]] = defaultdict(dict)
        
        # 实时价格缓存
        self._price_cache: Dict[str, Dict[str, float]] = defaultdict(dict)
        
    async def initialize(self) -> bool:
        """
        初始化概念数据
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("Initializing concept manager...")
            
            if self.data_source == "local":
                await self._load_local_data()
            elif self.data_source == "api":
                await self._load_api_data()
            elif self.data_source == "database":
                await self._load_database_data()
            else:
                raise ValueError(f"Unsupported data source: {self.data_source}")
            
            logger.info(f"Loaded {len(self._concepts)} concepts with {sum(len(c) for c in self._concept_constituents.values())} constituents")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize concept manager: {e}")
            return False
            
    async def _load_local_data(self) -> None:
        """加载本地数据"""
        # 示例数据，实际应从文件或数据库加载
        sample_concepts = [
            ConceptInfo("AI", "人工智能", "人工智能相关概念", "科技"),
            ConceptInfo("5G", "5G通信", "5G通信技术概念", "通信"),
            ConceptInfo("NEW_ENERGY", "新能源", "新能源汽车和清洁能源", "能源"),
            ConceptInfo("CHIP", "芯片", "半导体芯片概念", "科技"),
            ConceptInfo("BIOTECH", "生物医药", "生物技术和医药", "医药")
        ]
        
        for concept in sample_concepts:
            self._concepts[concept.code] = concept
            
        # 示例成份股数据
        sample_constituents = {
            "AI": ["000001.SZ", "000002.SZ", "600000.SH"],
            "5G": ["000001.SZ", "600001.SH", "600002.SH"],
            "NEW_ENERGY": ["000003.SZ", "600003.SH"],
            "CHIP": ["000004.SZ", "600004.SH", "600005.SH"],
            "BIOTECH": ["000005.SZ", "600006.SH"]
        }
        
        for concept_code, symbols in sample_constituents.items():
            for symbol in symbols:
                constituent = ConceptConstituent(
                    concept_code=concept_code,
                    symbol=symbol,
                    weight=1.0 / len(symbols),  # 等权重
                    join_date=datetime.now().strftime("%Y%m%d")
                )
                self._concept_constituents[concept_code].append(constituent)
                self._symbol_concepts[symbol].append(concept_code)
                
    async def _load_api_data(self) -> None:
        """从API加载数据"""
        # TODO: 实现API数据加载
        logger.warning("API data loading not implemented yet")
        await self._load_local_data()  # 临时使用本地数据
        
    async def _load_database_data(self) -> None:
        """从数据库加载数据"""
        # TODO: 实现数据库数据加载
        logger.warning("Database data loading not implemented yet")
        await self._load_local_data()  # 临时使用本地数据
        
    def get_concept_info(self, concept_code: str) -> Optional[ConceptInfo]:
        """获取概念信息"""
        return self._concepts.get(concept_code)
        
    def get_all_concepts(self) -> List[ConceptInfo]:
        """获取所有概念"""
        return list(self._concepts.values())
        
    def get_concept_constituents(self, concept_code: str) -> List[ConceptConstituent]:
        """获取概念成份股"""
        return self._concept_constituents.get(concept_code, [])
        
    def get_symbol_concepts(self, symbol: str) -> List[str]:
        """获取股票所属概念"""
        return self._symbol_concepts.get(symbol, [])
        
    def update_price(self, symbol: str, price: float, timestamp: Optional[str] = None) -> None:
        """
        更新股票价格
        
        Args:
            symbol: 股票代码
            price: 价格
            timestamp: 时间戳
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d %H:%M:%S")
            
        self._price_cache[symbol]["price"] = price
        self._price_cache[symbol]["timestamp"] = timestamp
        
    def calculate_concept_performance(self, concept_code: str, date_str: Optional[str] = None) -> Optional[ConceptPerformance]:
        """
        计算概念表现
        
        Args:
            concept_code: 概念代码
            date_str: 日期字符串
            
        Returns:
            Optional[ConceptPerformance]: 概念表现
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y%m%d")
                
            constituents = self.get_concept_constituents(concept_code)
            if not constituents:
                return None
                
            # 计算加权平均价格和涨跌幅
            total_weight = 0.0
            weighted_change = 0.0
            weighted_volume = 0
            weighted_turnover = 0.0
            up_count = 0
            down_count = 0
            flat_count = 0
            
            for constituent in constituents:
                symbol = constituent.symbol
                weight = constituent.weight
                
                # 获取价格数据（这里需要实际的价格数据）
                price_data = self._price_cache.get(symbol, {})
                current_price = price_data.get("price", 0.0)
                
                if current_price > 0:
                    # 假设有昨收价数据
                    prev_close = current_price * 0.98  # 示例数据
                    change_pct = (current_price - prev_close) / prev_close * 100
                    
                    weighted_change += change_pct * weight
                    total_weight += weight
                    
                    # 统计涨跌家数
                    if change_pct > 0:
                        up_count += 1
                    elif change_pct < 0:
                        down_count += 1
                    else:
                        flat_count += 1
                        
            if total_weight > 0:
                weighted_change /= total_weight
                
            performance = ConceptPerformance(
                concept_code=concept_code,
                date=date_str,
                change_pct=weighted_change,
                constituent_count=len(constituents),
                up_count=up_count,
                down_count=down_count,
                flat_count=flat_count
            )
            
            # 缓存结果
            self._concept_performance[concept_code][date_str] = performance
            
            return performance
            
        except Exception as e:
            logger.error(f"Error calculating concept performance for {concept_code}: {e}")
            return None
            
    def get_concept_performance(self, concept_code: str, date_str: str) -> Optional[ConceptPerformance]:
        """获取概念表现"""
        return self._concept_performance.get(concept_code, {}).get(date_str)
        
    def get_top_concepts(self, date_str: Optional[str] = None, limit: int = 10, sort_by: str = "change_pct") -> List[ConceptPerformance]:
        """
        获取表现最好的概念
        
        Args:
            date_str: 日期字符串
            limit: 返回数量限制
            sort_by: 排序字段
            
        Returns:
            List[ConceptPerformance]: 概念表现列表
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
            
        # 计算所有概念的表现
        performances = []
        for concept_code in self._concepts.keys():
            performance = self.calculate_concept_performance(concept_code, date_str)
            if performance:
                performances.append(performance)
                
        # 排序
        if sort_by == "change_pct":
            performances.sort(key=lambda x: x.change_pct, reverse=True)
        elif sort_by == "volume":
            performances.sort(key=lambda x: x.volume, reverse=True)
        elif sort_by == "turnover":
            performances.sort(key=lambda x: x.turnover, reverse=True)
            
        return performances[:limit]
        
    def get_concept_constituents_performance(self, concept_code: str, date_str: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取概念成份股表现
        
        Args:
            concept_code: 概念代码
            date_str: 日期字符串
            
        Returns:
            List[Dict[str, Any]]: 成份股表现列表
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y%m%d")
            
        constituents = self.get_concept_constituents(concept_code)
        performances = []
        
        for constituent in constituents:
            symbol = constituent.symbol
            price_data = self._price_cache.get(symbol, {})
            current_price = price_data.get("price", 0.0)
            
            if current_price > 0:
                # 计算涨跌幅（示例）
                prev_close = current_price * 0.98
                change_pct = (current_price - prev_close) / prev_close * 100
                
                performance = {
                    "symbol": symbol,
                    "weight": constituent.weight,
                    "current_price": current_price,
                    "change_pct": change_pct,
                    "contribution": change_pct * constituent.weight
                }
                performances.append(performance)
                
        # 按贡献度排序
        performances.sort(key=lambda x: x["contribution"], reverse=True)
        return performances
        
    def analyze_concept_rotation(self, days: int = 5) -> Dict[str, Any]:
        """
        分析概念轮动
        
        Args:
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 轮动分析结果
        """
        try:
            # 获取最近几天的概念表现
            rotation_data = {}
            
            for i in range(days):
                date_obj = datetime.now().replace(day=datetime.now().day - i)
                date_str = date_obj.strftime("%Y%m%d")
                
                top_concepts = self.get_top_concepts(date_str, limit=5)
                rotation_data[date_str] = [p.concept_code for p in top_concepts]
                
            # 分析轮动模式
            concept_frequency = defaultdict(int)
            for concepts in rotation_data.values():
                for concept in concepts:
                    concept_frequency[concept] += 1
                    
            # 热门概念
            hot_concepts = sorted(concept_frequency.items(), key=lambda x: x[1], reverse=True)
            
            return {
                "rotation_data": rotation_data,
                "hot_concepts": hot_concepts[:10],
                "analysis_period": days
            }
            
        except Exception as e:
            logger.error(f"Error analyzing concept rotation: {e}")
            return {}
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_concepts": len(self._concepts),
            "total_constituents": sum(len(c) for c in self._concept_constituents.values()),
            "cached_prices": len(self._price_cache),
            "performance_records": sum(len(p) for p in self._concept_performance.values())
        }
