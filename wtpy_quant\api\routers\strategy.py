"""
策略管理API路由

提供策略相关的REST API接口，包括：
- 策略列表查询
- 策略详情获取
- 策略参数配置
- 策略启停控制
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from ...core.engine import WtpyEngine
from ...core.strategy_manager import StrategyManager
from ...strategy import BaseStrategy, ConceptStrategy, MicrostructureStrategy

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic模型定义
class StrategyInfo(BaseModel):
    """策略信息"""
    strategy_id: str
    name: str
    type: str
    status: str
    description: Optional[str] = None
    parameters: Dict[str, Any] = {}
    create_time: Optional[str] = None
    update_time: Optional[str] = None


class StrategyCreateRequest(BaseModel):
    """创建策略请求"""
    name: str = Field(..., description="策略名称")
    type: str = Field(..., description="策略类型")
    description: Optional[str] = Field(None, description="策略描述")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="策略参数")


class StrategyUpdateRequest(BaseModel):
    """更新策略请求"""
    name: Optional[str] = Field(None, description="策略名称")
    description: Optional[str] = Field(None, description="策略描述")
    parameters: Optional[Dict[str, Any]] = Field(None, description="策略参数")


class StrategyControlRequest(BaseModel):
    """策略控制请求"""
    action: str = Field(..., description="操作类型: start, stop, restart")


# 依赖注入
def get_strategy_manager() -> StrategyManager:
    """获取策略管理器"""
    # 这里应该从主应用获取，简化处理
    from ...api.main import app_state
    if not app_state['initialized'] or not app_state['engine']:
        raise HTTPException(status_code=503, detail="Strategy manager not available")
    return app_state['engine'].strategy_manager


@router.get("/", response_model=List[StrategyInfo])
async def list_strategies(
    status: Optional[str] = None,
    strategy_type: Optional[str] = None,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    获取策略列表
    
    Args:
        status: 策略状态过滤 (active, inactive, error)
        strategy_type: 策略类型过滤
    """
    try:
        strategies = strategy_manager.list_strategies()
        
        # 转换为API模型
        strategy_list = []
        for strategy_id, strategy_info in strategies.items():
            api_info = StrategyInfo(
                strategy_id=strategy_id,
                name=strategy_info.name,
                type=strategy_info.strategy_type,
                status=strategy_info.status.value,
                description=strategy_info.description,
                parameters=strategy_info.parameters,
                create_time=strategy_info.create_time,
                update_time=strategy_info.update_time
            )
            
            # 应用过滤条件
            if status and api_info.status != status:
                continue
            if strategy_type and api_info.type != strategy_type:
                continue
                
            strategy_list.append(api_info)
            
        return strategy_list
        
    except Exception as e:
        logger.error(f"Error listing strategies: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=StrategyInfo)
async def create_strategy(
    request: StrategyCreateRequest,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    创建新策略
    """
    try:
        # 验证策略类型
        supported_types = ["ConceptStrategy", "MicrostructureStrategy"]
        if request.type not in supported_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported strategy type. Supported types: {supported_types}"
            )
            
        # 创建策略实例
        if request.type == "ConceptStrategy":
            strategy = ConceptStrategy(name=request.name, **request.parameters)
        elif request.type == "MicrostructureStrategy":
            strategy = MicrostructureStrategy(name=request.name, **request.parameters)
        else:
            raise HTTPException(status_code=400, detail="Invalid strategy type")
            
        # 注册策略
        strategy_id = strategy_manager.register_strategy(
            strategy=strategy,
            description=request.description
        )
        
        # 获取策略信息
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        
        return StrategyInfo(
            strategy_id=strategy_id,
            name=strategy_info.name,
            type=strategy_info.strategy_type,
            status=strategy_info.status.value,
            description=strategy_info.description,
            parameters=strategy_info.parameters,
            create_time=strategy_info.create_time,
            update_time=strategy_info.update_time
        )
        
    except Exception as e:
        logger.error(f"Error creating strategy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{strategy_id}", response_model=StrategyInfo)
async def get_strategy(
    strategy_id: str,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    获取策略详情
    """
    try:
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        return StrategyInfo(
            strategy_id=strategy_id,
            name=strategy_info.name,
            type=strategy_info.strategy_type,
            status=strategy_info.status.value,
            description=strategy_info.description,
            parameters=strategy_info.parameters,
            create_time=strategy_info.create_time,
            update_time=strategy_info.update_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{strategy_id}", response_model=StrategyInfo)
async def update_strategy(
    strategy_id: str,
    request: StrategyUpdateRequest,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    更新策略配置
    """
    try:
        # 检查策略是否存在
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        # 更新策略信息
        update_data = {}
        if request.name is not None:
            update_data['name'] = request.name
        if request.description is not None:
            update_data['description'] = request.description
        if request.parameters is not None:
            update_data['parameters'] = request.parameters
            
        success = strategy_manager.update_strategy(strategy_id, **update_data)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to update strategy")
            
        # 返回更新后的策略信息
        updated_info = strategy_manager.get_strategy_info(strategy_id)
        
        return StrategyInfo(
            strategy_id=strategy_id,
            name=updated_info.name,
            type=updated_info.strategy_type,
            status=updated_info.status.value,
            description=updated_info.description,
            parameters=updated_info.parameters,
            create_time=updated_info.create_time,
            update_time=updated_info.update_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating strategy {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: str,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    删除策略
    """
    try:
        # 检查策略是否存在
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        # 如果策略正在运行，先停止
        if strategy_info.status.value == "active":
            strategy_manager.stop_strategy(strategy_id)
            
        # 删除策略
        success = strategy_manager.unregister_strategy(strategy_id)
        if not success:
            raise HTTPException(status_code=400, detail="Failed to delete strategy")
            
        return {"message": f"Strategy {strategy_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting strategy {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/{strategy_id}/control")
async def control_strategy(
    strategy_id: str,
    request: StrategyControlRequest,
    background_tasks: BackgroundTasks,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    控制策略运行状态
    """
    try:
        # 检查策略是否存在
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        # 执行控制操作
        if request.action == "start":
            success = strategy_manager.start_strategy(strategy_id)
            message = f"Strategy {strategy_id} started"
        elif request.action == "stop":
            success = strategy_manager.stop_strategy(strategy_id)
            message = f"Strategy {strategy_id} stopped"
        elif request.action == "restart":
            # 先停止再启动
            strategy_manager.stop_strategy(strategy_id)
            success = strategy_manager.start_strategy(strategy_id)
            message = f"Strategy {strategy_id} restarted"
        else:
            raise HTTPException(
                status_code=400, 
                detail="Invalid action. Supported actions: start, stop, restart"
            )
            
        if not success:
            raise HTTPException(status_code=400, detail=f"Failed to {request.action} strategy")
            
        return {"message": message}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error controlling strategy {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{strategy_id}/stats")
async def get_strategy_stats(
    strategy_id: str,
    strategy_manager: StrategyManager = Depends(get_strategy_manager)
):
    """
    获取策略统计信息
    """
    try:
        # 检查策略是否存在
        strategy_info = strategy_manager.get_strategy_info(strategy_id)
        if not strategy_info:
            raise HTTPException(status_code=404, detail="Strategy not found")
            
        # 获取策略实例
        strategy = strategy_manager.get_strategy(strategy_id)
        if not strategy:
            raise HTTPException(status_code=404, detail="Strategy instance not found")
            
        # 获取统计信息
        stats = strategy.get_stats()
        
        # 如果策略有特定的信息获取方法，调用它
        if hasattr(strategy, 'get_strategy_info'):
            strategy_specific_info = strategy.get_strategy_info()
            stats.update(strategy_specific_info)
            
        return {
            "strategy_id": strategy_id,
            "stats": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting strategy stats {strategy_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/types/supported")
async def get_supported_strategy_types():
    """
    获取支持的策略类型
    """
    return {
        "supported_types": [
            {
                "type": "ConceptStrategy",
                "name": "概念板块选股策略",
                "description": "基于概念轮动的选股策略",
                "parameters": {
                    "max_positions": {"type": "int", "default": 10, "description": "最大持仓数"},
                    "position_size": {"type": "float", "default": 0.1, "description": "单个持仓大小"},
                    "stop_loss": {"type": "float", "default": -0.05, "description": "止损比例"},
                    "take_profit": {"type": "float", "default": 0.15, "description": "止盈比例"}
                }
            },
            {
                "type": "MicrostructureStrategy", 
                "name": "微结构因子策略",
                "description": "基于高频数据的微观结构因子策略",
                "parameters": {
                    "target_symbols": {"type": "list", "default": ["000001.SZ"], "description": "目标股票"},
                    "tick_window": {"type": "int", "default": 100, "description": "Tick窗口大小"},
                    "signal_threshold": {"type": "float", "default": 0.6, "description": "信号阈值"},
                    "position_size": {"type": "int", "default": 1000, "description": "单次交易股数"}
                }
            }
        ]
    }
