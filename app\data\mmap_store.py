"""
Minimal MmapStore interface sketch for high-frequency intraday caching.
This is a placeholder to clarify APIs; actual impl will be added once xtdata/wtpy wiring is in place.
"""
from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

import os
import struct

@dataclass
class TickRecord:
    ts: int  # epoch nano or ms
    price: float
    volume: int
    side: int  # 1 buy, -1 sell, 0 unknown


class MmapStore:
    def __init__(self, root: Path):
        self.root = Path(root)
        self.root.mkdir(parents=True, exist_ok=True)

    def today_tick_path(self, symbol: str) -> Path:
        return self.root / symbol / "tick" / "today.bin"

    def get_recent_ticks(self, symbol: str, k: int = 4) -> list[TickRecord]:
        # Placeholder: returns empty list; real impl will use numpy.memmap
        return []

