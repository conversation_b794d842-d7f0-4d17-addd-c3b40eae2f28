from pathlib import Path
import argparse
from app.core.wtpy_integration import BacktestFacade


def main():
    p = argparse.ArgumentParser(description="Run wtpy backtest")
    p.add_argument("strategy", help="Path to strategy script or name referenced by cfg")
    p.add_argument("--config", default=str(Path("app/config/engine.config.json").resolve()))
    p.add_argument("--out", default=str(Path("runs/backtest").resolve()))
    args = p.parse_args()

    fac = BacktestFacade()
    res = fac.run(strategy_py=args.strategy, config_path=args.config, out_dir=args.out)
    print({"ok": res.ok, "message": res.message, "details": res.details})


if __name__ == "__main__":
    main()

