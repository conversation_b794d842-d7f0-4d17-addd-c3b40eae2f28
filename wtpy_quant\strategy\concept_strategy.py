"""
概念板块选股策略 - 基于概念轮动的选股策略

策略逻辑：
1. 监控概念板块表现，识别热门概念
2. 在热门概念中选择技术面较好的个股
3. 动态调整持仓，跟随概念轮动
4. 风险控制和止盈止损
"""

import logging
from typing import Any, Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from .base_strategy import BaseStrategy
from .indicators import sma, ema, rsi, macd
from ..data.concept_manager import ConceptManager

logger = logging.getLogger(__name__)


class ConceptStrategy(BaseStrategy):
    """概念板块选股策略"""
    
    def __init__(self, name: str = "ConceptStrategy", **kwargs):
        """
        初始化策略
        
        Args:
            name: 策略名称
            **kwargs: 策略参数
        """
        super().__init__(name, **kwargs)
        
        # 策略参数
        self.max_positions = self.get_param("max_positions", 10)  # 最大持仓数
        self.concept_lookback = self.get_param("concept_lookback", 5)  # 概念表现回看天数
        self.min_concept_return = self.get_param("min_concept_return", 0.02)  # 最小概念涨幅
        self.position_size = self.get_param("position_size", 0.1)  # 单个持仓大小
        self.stop_loss = self.get_param("stop_loss", -0.05)  # 止损比例
        self.take_profit = self.get_param("take_profit", 0.15)  # 止盈比例
        self.rebalance_freq = self.get_param("rebalance_freq", "daily")  # 调仓频率
        
        # 技术指标参数
        self.rsi_period = self.get_param("rsi_period", 14)
        self.rsi_oversold = self.get_param("rsi_oversold", 30)
        self.rsi_overbought = self.get_param("rsi_overbought", 70)
        self.ma_short = self.get_param("ma_short", 5)
        self.ma_long = self.get_param("ma_long", 20)
        
        # 概念管理器
        self.concept_manager: Optional[ConceptManager] = None
        
        # 候选股票池
        self.candidate_stocks: List[str] = []
        self.hot_concepts: List[str] = []
        
        # 持仓成本记录
        self.position_costs: Dict[str, float] = {}
        
        # 最后调仓时间
        self.last_rebalance_time: Optional[datetime] = None
        
        logger.info(f"ConceptStrategy initialized with max_positions={self.max_positions}")
        
    def on_init(self, context: Any) -> None:
        """策略初始化"""
        try:
            self.log_info("策略初始化开始")
            
            # 初始化概念管理器
            self.concept_manager = ConceptManager("local")
            # 注意：在实际使用中需要异步初始化
            
            # 订阅数据
            self._subscribe_data(context)
            
            # 设置定时器
            context.stra_set_timer(1, 60)  # 每分钟检查一次
            
            self._initialized = True
            self.log_info("策略初始化完成")
            
        except Exception as e:
            self.log_error(f"策略初始化失败: {e}")
            
    def on_tick(self, context: Any, stdCode: str, newTick: Any) -> None:
        """Tick数据回调"""
        try:
            # 更新价格缓存
            self._tick_cache[stdCode] = newTick
            
            # 检查止盈止损
            self._check_stop_conditions(context, stdCode)
            
        except Exception as e:
            self.log_error(f"处理Tick数据失败 {stdCode}: {e}")
            
    def on_bar(self, context: Any, stdCode: str, period: str, newBar: Any) -> None:
        """Bar数据回调"""
        try:
            if period != "m1":  # 只处理1分钟K线
                return
                
            # 更新概念管理器价格
            if self.concept_manager:
                price = getattr(newBar, 'close', 0)
                if price > 0:
                    self.concept_manager.update_price(stdCode, price)
                    
            # 检查是否需要调仓
            if self._should_rebalance():
                self._rebalance_portfolio(context)
                
        except Exception as e:
            self.log_error(f"处理Bar数据失败 {stdCode}: {e}")
            
    def on_timer(self, context: Any, id: int, times: int) -> None:
        """定时器回调"""
        try:
            if id == 1:  # 主定时器
                # 更新热门概念
                self._update_hot_concepts()
                
                # 更新候选股票池
                self._update_candidate_stocks()
                
                # 检查调仓条件
                if self._should_rebalance():
                    self._rebalance_portfolio(context)
                    
        except Exception as e:
            self.log_error(f"定时器处理失败: {e}")
            
    def _subscribe_data(self, context: Any) -> None:
        """订阅数据"""
        try:
            # 这里可以订阅指数数据用于基准比较
            # context.stra_sub_ticks("000300.SH")  # 沪深300
            pass
            
        except Exception as e:
            self.log_error(f"订阅数据失败: {e}")
            
    def _update_hot_concepts(self) -> None:
        """更新热门概念"""
        try:
            if not self.concept_manager:
                return
                
            # 获取热门概念
            hot_concepts = self.concept_manager.get_top_concepts(limit=20)
            
            # 过滤涨幅足够的概念
            filtered_concepts = []
            for concept_perf in hot_concepts:
                if concept_perf.change_pct >= self.min_concept_return:
                    filtered_concepts.append(concept_perf.concept_code)
                    
            self.hot_concepts = filtered_concepts[:10]  # 取前10个
            
            if self.hot_concepts:
                self.log_info(f"热门概念更新: {', '.join(self.hot_concepts)}")
            else:
                self.log_info("当前无热门概念")
                
        except Exception as e:
            self.log_error(f"更新热门概念失败: {e}")
            
    def _update_candidate_stocks(self) -> None:
        """更新候选股票池"""
        try:
            if not self.concept_manager or not self.hot_concepts:
                self.candidate_stocks = []
                return
                
            candidates = set()
            
            # 从热门概念中选择成分股
            for concept_code in self.hot_concepts:
                constituents = self.concept_manager.get_concept_constituents(concept_code)
                
                # 按权重排序，选择权重较高的股票
                constituents.sort(key=lambda x: x.weight, reverse=True)
                
                # 每个概念最多选择5只股票
                for constituent in constituents[:5]:
                    candidates.add(constituent.symbol)
                    
            self.candidate_stocks = list(candidates)
            
            if self.candidate_stocks:
                self.log_info(f"候选股票池更新: {len(self.candidate_stocks)}只股票")
            else:
                self.log_info("候选股票池为空")
                
        except Exception as e:
            self.log_error(f"更新候选股票池失败: {e}")
            
    def _should_rebalance(self) -> bool:
        """判断是否应该调仓"""
        try:
            if not self._initialized:
                return False
                
            now = datetime.now()
            
            # 首次调仓
            if self.last_rebalance_time is None:
                return True
                
            # 根据调仓频率判断
            if self.rebalance_freq == "daily":
                return now.date() > self.last_rebalance_time.date()
            elif self.rebalance_freq == "hourly":
                return (now - self.last_rebalance_time).total_seconds() >= 3600
            else:
                return False
                
        except Exception as e:
            self.log_error(f"判断调仓条件失败: {e}")
            return False
            
    def _rebalance_portfolio(self, context: Any) -> None:
        """调仓"""
        try:
            self.log_info("开始调仓")
            
            # 获取当前持仓
            current_positions = {}
            for symbol in self.candidate_stocks:
                pos = self.get_position(context, symbol)
                if abs(pos) > 0:
                    current_positions[symbol] = pos
                    
            # 选择目标股票
            target_stocks = self._select_target_stocks(context)
            
            # 计算目标持仓
            target_positions = {}
            if target_stocks:
                position_weight = min(self.position_size, 1.0 / len(target_stocks))
                
                for symbol in target_stocks:
                    # 这里简化处理，实际应该根据资金计算股数
                    target_positions[symbol] = position_weight
                    
            # 执行调仓
            self._execute_rebalance(context, current_positions, target_positions)
            
            self.last_rebalance_time = datetime.now()
            self.log_info(f"调仓完成，目标持仓: {len(target_positions)}只股票")
            
        except Exception as e:
            self.log_error(f"调仓失败: {e}")
            
    def _select_target_stocks(self, context: Any) -> List[str]:
        """选择目标股票"""
        try:
            if not self.candidate_stocks:
                return []
                
            scored_stocks = []
            
            for symbol in self.candidate_stocks:
                score = self._calculate_stock_score(context, symbol)
                if score > 0:
                    scored_stocks.append((symbol, score))
                    
            # 按得分排序
            scored_stocks.sort(key=lambda x: x[1], reverse=True)
            
            # 选择前N只股票
            target_count = min(self.max_positions, len(scored_stocks))
            target_stocks = [stock[0] for stock in scored_stocks[:target_count]]
            
            return target_stocks
            
        except Exception as e:
            self.log_error(f"选择目标股票失败: {e}")
            return []
            
    def _calculate_stock_score(self, context: Any, symbol: str) -> float:
        """计算股票得分"""
        try:
            # 获取K线数据
            bars_df = self.get_bars(context, symbol, "m1", 100)
            if bars_df is None or len(bars_df) < 50:
                return 0.0
                
            close_prices = bars_df['close']
            
            score = 0.0
            
            # 技术指标得分
            
            # 1. RSI指标 - 寻找超卖但不过度超卖的股票
            rsi_values = rsi(close_prices, self.rsi_period)
            latest_rsi = rsi_values.iloc[-1]
            
            if 25 <= latest_rsi <= 45:  # 轻度超卖
                score += 2.0
            elif 45 < latest_rsi <= 60:  # 中性偏强
                score += 1.0
            elif latest_rsi > 75:  # 过度超买
                score -= 2.0
                
            # 2. 均线趋势
            ma_short_values = sma(close_prices, self.ma_short)
            ma_long_values = sma(close_prices, self.ma_long)
            
            if ma_short_values.iloc[-1] > ma_long_values.iloc[-1]:
                score += 1.5  # 短期均线在长期均线之上
                
            # 3. 价格相对位置
            recent_high = close_prices.tail(20).max()
            recent_low = close_prices.tail(20).min()
            current_price = close_prices.iloc[-1]
            
            if recent_high > recent_low:
                price_position = (current_price - recent_low) / (recent_high - recent_low)
                if 0.3 <= price_position <= 0.7:  # 价格在中等位置
                    score += 1.0
                    
            # 4. 成交量确认（如果有成交量数据）
            if 'volume' in bars_df.columns:
                volume = bars_df['volume']
                avg_volume = volume.tail(20).mean()
                recent_volume = volume.tail(5).mean()
                
                if recent_volume > avg_volume * 1.2:  # 成交量放大
                    score += 0.5
                    
            # 5. 概念热度加分
            if self.concept_manager:
                symbol_concepts = self.concept_manager.get_symbol_concepts(symbol)
                hot_concept_count = len(set(symbol_concepts) & set(self.hot_concepts))
                score += hot_concept_count * 0.5
                
            return max(0.0, score)
            
        except Exception as e:
            self.log_error(f"计算股票得分失败 {symbol}: {e}")
            return 0.0
            
    def _execute_rebalance(self, context: Any, 
                          current_positions: Dict[str, float], 
                          target_positions: Dict[str, float]) -> None:
        """执行调仓"""
        try:
            # 卖出不在目标持仓中的股票
            for symbol, current_pos in current_positions.items():
                if symbol not in target_positions and abs(current_pos) > 0:
                    self.sell(context, symbol, abs(current_pos))
                    self.log_info(f"卖出 {symbol}: {current_pos}")
                    
            # 调整目标持仓
            for symbol, target_pos in target_positions.items():
                current_pos = current_positions.get(symbol, 0)
                
                if abs(target_pos - current_pos) > 0.01:  # 有显著差异才调整
                    if target_pos > current_pos:
                        # 买入
                        qty = target_pos - current_pos
                        if self.buy(context, symbol, qty):
                            # 记录成本价
                            price = self.get_price(context, symbol)
                            self.position_costs[symbol] = price
                            self.log_info(f"买入 {symbol}: {qty}@{price}")
                    else:
                        # 卖出
                        qty = current_pos - target_pos
                        self.sell(context, symbol, qty)
                        self.log_info(f"减仓 {symbol}: {qty}")
                        
        except Exception as e:
            self.log_error(f"执行调仓失败: {e}")
            
    def _check_stop_conditions(self, context: Any, symbol: str) -> None:
        """检查止盈止损条件"""
        try:
            position = self.get_position(context, symbol)
            if abs(position) < 1:  # 无持仓
                return
                
            current_price = self.get_price(context, symbol)
            cost_price = self.position_costs.get(symbol)
            
            if not cost_price or current_price <= 0:
                return
                
            # 计算盈亏比例
            pnl_ratio = (current_price - cost_price) / cost_price
            
            should_close = False
            reason = ""
            
            # 止损检查
            if pnl_ratio <= self.stop_loss:
                should_close = True
                reason = f"止损 (亏损{pnl_ratio:.2%})"
                
            # 止盈检查
            elif pnl_ratio >= self.take_profit:
                should_close = True
                reason = f"止盈 (盈利{pnl_ratio:.2%})"
                
            if should_close:
                self.sell(context, symbol, abs(position))
                if symbol in self.position_costs:
                    del self.position_costs[symbol]
                self.log_info(f"{reason}: {symbol} {position}@{current_price}")
                
        except Exception as e:
            self.log_error(f"检查止盈止损失败 {symbol}: {e}")
            
    def get_strategy_info(self) -> Dict[str, Any]:
        """获取策略信息"""
        info = {
            'name': self.name,
            'type': 'concept_rotation',
            'max_positions': self.max_positions,
            'hot_concepts': self.hot_concepts,
            'candidate_stocks_count': len(self.candidate_stocks),
            'current_positions_count': len([p for p in self._positions.values() if abs(p) > 0]),
            'last_rebalance_time': self.last_rebalance_time.isoformat() if self.last_rebalance_time else None
        }
        info.update(self.get_stats())
        return info
