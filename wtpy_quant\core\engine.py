"""
wtpy引擎封装 - 核心引擎管理

负责wtpy引擎的初始化、配置和生命周期管理，包括：
- 引擎创建与配置
- 数据源与交易接口集成
- 策略注册与管理
- 引擎启动与停止
"""

import asyncio
import logging
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import time

from .config_manager import Config
from .event_bridge import EventBridge, EventType, Event, WtpyEventAdapter
from .strategy_manager import StrategyManager

logger = logging.getLogger(__name__)


class WtpyEngineError(Exception):
    """wtpy引擎异常"""
    pass


class WtpyEngine:
    """wtpy引擎封装类"""
    
    def __init__(self, config: Config):
        """
        初始化wtpy引擎
        
        Args:
            config: 系统配置
        """
        self.config = config
        self.event_bridge = EventBridge()
        self.strategy_manager = StrategyManager(self.event_bridge, config.engine.strategy_path)
        self.wtpy_adapter = WtpyEventAdapter(self.event_bridge)
        
        # wtpy引擎实例
        self._engine: Optional[Any] = None
        self._backtest_engine: Optional[Any] = None
        
        # 运行状态
        self._running = False
        self._mode = config.engine.mode  # live / backtest
        
        # 统计信息
        self._stats = {
            'start_time': 0,
            'uptime': 0,
            'events_processed': 0,
            'strategies_loaded': 0
        }
        
    async def initialize(self) -> bool:
        """
        初始化引擎
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("Initializing wtpy engine...")
            
            # 检查wtpy是否可用
            if not self._check_wtpy_available():
                raise WtpyEngineError("wtpy is not available")
            
            # 启动事件桥接器
            await self.event_bridge.start()
            
            # 创建wtpy引擎实例
            if self._mode == "live":
                await self._initialize_live_engine()
            elif self._mode == "backtest":
                await self._initialize_backtest_engine()
            else:
                raise WtpyEngineError(f"Unknown engine mode: {self._mode}")
            
            # 自动发现策略
            self.strategy_manager.discover_strategies()
            
            logger.info("wtpy engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize wtpy engine: {e}")
            return False
            
    async def _initialize_live_engine(self) -> None:
        """初始化实盘引擎"""
        try:
            import wtpy
            from wtpy import WtEngine
            
            # 创建引擎实例
            self._engine = WtEngine()
            
            # 生成wtpy配置文件
            config_path = await self._generate_wtpy_config()
            
            # 初始化引擎
            self._engine.init(str(config_path))
            
            # 注册事件回调
            self._register_engine_callbacks()
            
            logger.info("Live engine initialized")
            
        except ImportError:
            raise WtpyEngineError("wtpy not installed")
        except Exception as e:
            raise WtpyEngineError(f"Failed to initialize live engine: {e}")
            
    async def _initialize_backtest_engine(self) -> None:
        """初始化回测引擎"""
        try:
            import wtpy
            from wtpy import WtBtEngine
            
            # 创建回测引擎实例
            self._backtest_engine = WtBtEngine()
            
            # 生成wtpy配置文件
            config_path = await self._generate_wtpy_config()
            
            # 设置输出目录
            output_dir = Path(self.config.engine.data_path) / "backtest_output"
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化回测引擎
            self._backtest_engine.init(str(output_dir), str(config_path))
            
            # 注册事件回调
            self._register_backtest_callbacks()
            
            logger.info("Backtest engine initialized")
            
        except ImportError:
            raise WtpyEngineError("wtpy not installed")
        except Exception as e:
            raise WtpyEngineError(f"Failed to initialize backtest engine: {e}")
            
    def _register_engine_callbacks(self) -> None:
        """注册引擎回调函数"""
        if not self._engine:
            return
            
        # 注册策略回调（如果引擎支持）
        try:
            # 这里需要根据实际的wtpy版本调整
            if hasattr(self._engine, 'set_external_cta'):
                self._engine.set_external_cta(self.wtpy_adapter)
            elif hasattr(self._engine, 'set_external_evt'):
                self._engine.set_external_evt(self.wtpy_adapter)
        except Exception as e:
            logger.warning(f"Failed to register engine callbacks: {e}")
            
    def _register_backtest_callbacks(self) -> None:
        """注册回测引擎回调函数"""
        if not self._backtest_engine:
            return
            
        try:
            # 注册回测回调
            if hasattr(self._backtest_engine, 'set_external_cta'):
                self._backtest_engine.set_external_cta(self.wtpy_adapter)
            elif hasattr(self._backtest_engine, 'set_external_evt'):
                self._backtest_engine.set_external_evt(self.wtpy_adapter)
        except Exception as e:
            logger.warning(f"Failed to register backtest callbacks: {e}")
            
    async def _generate_wtpy_config(self) -> Path:
        """
        生成wtpy配置文件
        
        Returns:
            Path: 配置文件路径
        """
        config_dir = Path(self.config.engine.config_path)
        config_dir.mkdir(parents=True, exist_ok=True)
        
        config_file = config_dir / "engine.config.json"
        
        # 构建wtpy配置
        wtpy_config = {
            "version": "0.1.0",
            "data": {
                "source": self.config.data.market_source,
                "subscriptions": {
                    "tick": [],  # 将由策略动态订阅
                    "bar": {}
                }
            },
            "accounts": [
                {
                    "id": "default",
                    "broker": self.config.trading.broker,
                    "risk": {
                        "daily_max_loss": self.config.trading.risk.max_daily_loss_ratio
                    }
                }
            ],
            "strategies": []
        }
        
        # 添加已注册的策略
        for strategy_info in self.strategy_manager.list_strategies():
            strategy_config = {
                "name": strategy_info.name,
                "module": f"{strategy_info.module_path}:{strategy_info.class_name}",
                "enabled": strategy_info.state.value == "running",
                "params": strategy_info.params
            }
            wtpy_config["strategies"].append(strategy_config)
        
        # 保存配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(wtpy_config, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Generated wtpy config: {config_file}")
        return config_file

    def _check_wtpy_available(self) -> bool:
        """检查wtpy是否可用"""
        try:
            import wtpy
            return True
        except ImportError:
            logger.error("wtpy is not installed. Please install wondertrader/wtpy.")
            return False

    async def start(self) -> bool:
        """
        启动引擎

        Returns:
            bool: 启动是否成功
        """
        try:
            if self._running:
                logger.warning("Engine is already running")
                return True

            logger.info("Starting wtpy engine...")

            # 初始化引擎（如果未初始化）
            if self._engine is None and self._backtest_engine is None:
                if not await self.initialize():
                    return False

            # 启动引擎
            if self._mode == "live":
                await self._start_live_engine()
            elif self._mode == "backtest":
                await self._start_backtest_engine()

            self._running = True
            self._stats['start_time'] = time.time()

            # 发送引擎启动事件
            await self.event_bridge.emit(Event(
                type=EventType.ENGINE_START,
                data={"mode": self._mode, "config": self.config},
                source="wtpy_engine"
            ))

            logger.info("wtpy engine started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start wtpy engine: {e}")

            # 发送引擎错误事件
            await self.event_bridge.emit(Event(
                type=EventType.ENGINE_ERROR,
                data={"error": str(e)},
                source="wtpy_engine"
            ))
            return False

    async def _start_live_engine(self) -> None:
        """启动实盘引擎"""
        if not self._engine:
            raise WtpyEngineError("Live engine not initialized")

        try:
            # 启动引擎（非阻塞）
            if hasattr(self._engine, 'run'):
                # 在后台线程中运行
                import threading
                engine_thread = threading.Thread(target=self._engine.run)
                engine_thread.daemon = True
                engine_thread.start()
            elif hasattr(self._engine, 'start'):
                self._engine.start()
            else:
                raise WtpyEngineError("Unknown wtpy engine start method")

            logger.info("Live engine started")

        except Exception as e:
            raise WtpyEngineError(f"Failed to start live engine: {e}")

    async def _start_backtest_engine(self) -> None:
        """启动回测引擎"""
        if not self._backtest_engine:
            raise WtpyEngineError("Backtest engine not initialized")

        try:
            # 运行回测（阻塞）
            if hasattr(self._backtest_engine, 'run_backtest'):
                # 在后台线程中运行回测
                import threading
                backtest_thread = threading.Thread(target=self._backtest_engine.run_backtest)
                backtest_thread.daemon = True
                backtest_thread.start()
            elif hasattr(self._backtest_engine, 'runBt'):
                import threading
                backtest_thread = threading.Thread(target=self._backtest_engine.runBt)
                backtest_thread.daemon = True
                backtest_thread.start()
            else:
                raise WtpyEngineError("Unknown wtpy backtest run method")

            logger.info("Backtest engine started")

        except Exception as e:
            raise WtpyEngineError(f"Failed to start backtest engine: {e}")

    async def stop(self) -> bool:
        """
        停止引擎

        Returns:
            bool: 停止是否成功
        """
        try:
            if not self._running:
                logger.warning("Engine is not running")
                return True

            logger.info("Stopping wtpy engine...")

            # 停止所有运行中的策略
            for strategy_name in self.strategy_manager.get_running_strategies():
                self.strategy_manager.stop_strategy(strategy_name)

            # 停止引擎
            if self._engine and hasattr(self._engine, 'stop'):
                self._engine.stop()
            if self._backtest_engine and hasattr(self._backtest_engine, 'stop'):
                self._backtest_engine.stop()

            # 停止事件桥接器
            await self.event_bridge.stop()

            self._running = False

            # 发送引擎停止事件
            await self.event_bridge.emit(Event(
                type=EventType.ENGINE_STOP,
                data={"uptime": time.time() - self._stats['start_time']},
                source="wtpy_engine"
            ))

            logger.info("wtpy engine stopped successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to stop wtpy engine: {e}")
            return False

    def is_running(self) -> bool:
        """检查引擎是否运行中"""
        return self._running

    def get_mode(self) -> str:
        """获取引擎模式"""
        return self._mode

    def get_stats(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        stats = self._stats.copy()
        if self._running and stats['start_time'] > 0:
            stats['uptime'] = time.time() - stats['start_time']

        # 添加其他模块的统计信息
        stats.update({
            'event_bridge': self.event_bridge.get_stats(),
            'strategy_manager': self.strategy_manager.get_stats()
        })

        return stats

    def get_engine_instance(self) -> Optional[Any]:
        """获取wtpy引擎实例（用于高级操作）"""
        return self._engine or self._backtest_engine
