"""
A minimal example strategy showing the expected wtpy-style lifecycle.
This is illustrative; when used with wtpy, adapt base class and registration per wtpy version.
"""
from __future__ import annotations
from typing import Any


class DemoCtaStrategy:
    def __init__(self, name: str = "demo_cta", symbol: str = "SH600000"):
        self.name = name
        self.symbol = symbol

    # Align with wtpy lifecycle names when integrating
    def on_init(self, ctx: Any):
        print(f"[{self.name}] init for {self.symbol}")

    def on_tick(self, ctx: Any, tick: Any):
        pass

    def on_bar(self, ctx: Any, bar: Any):
        pass

    def on_timer(self, ctx: Any):
        pass

