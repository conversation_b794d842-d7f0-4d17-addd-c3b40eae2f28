# WTpy/WonderTrader A-Share Quant System (Scaffold)

This repository contains a minimal scaffold aligned with 说明.MD to develop an A-share quant system using wtpy/wondertrader and miniqmt.

## Structure
- app/
  - main.py: FastAPI entrypoint with basic routers
  - api/: health, strategies, accounts, data, backtest
  - core/: future engine integration
  - data/: MmapStore & LongTermStore placeholders
  - exec/: BrokerAdapter placeholder
  - backtest/: wtpy backtest integration placeholder
  - strategy/: strategy template
  - config/engine.config.json: example config

## Run (once you install deps)
- pip install "fastapi>=0.110" "uvicorn[standard]>=0.23"
- uvicorn app.main:app --reload

### API endpoints
- GET /health/ping
- POST /bt/run           # wtpy 回测（BacktestFacade，支持动态加载策略）
- POST /live/run         # wtpy 实盘（LiveFacade）
- GET/POST /strategies   # 简单策略注册占位
- GET /accounts          # 账户占位
- GET /data/tick|bar     # 数据占位

### Web GUI（无需前端依赖）
- 服务启动后访问 http://127.0.0.1:8000/ui
- 包含健康检查、回测、实盘、策略/账户/数据操作入口

## Next
- Wire miniqmt/xtdata to feed wtpy engine
- Implement MmapStore (numpy.memmap) for intraday recent-K access
- Add BrokerAdapter for miniqmt and order routing/risk
- Provide wtpy backtest integration and example strategy

