"""
A minimal strategy template outline compatible with wtpy-like lifecycle names.
This is only a placeholder class for future alignment with wtpy base classes.
"""
from __future__ import annotations
from typing import Any


class StrategyTemplate:
    def on_init(self, ctx: Any):
        pass

    def on_tick(self, ctx: Any, tick: Any):
        pass

    def on_bar(self, ctx: Any, bar: Any):
        pass

    def on_order(self, ctx: Any, order: Any):
        pass

    def on_trade(self, ctx: Any, trade: Any):
        pass

    def on_timer(self, ctx: Any):
        pass

