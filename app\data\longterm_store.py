"""
Abstract LongTermStore facade. Initially supports simple Parquet/CSV sinks in data/parquet or data/csv.
"""
from __future__ import annotations
from dataclasses import dataclass
from pathlib import Path


@dataclass
class BarRecord:
    ts: int
    open: float
    high: float
    low: float
    close: float
    volume: float


class LongTermStore:
    def __init__(self, root: Path):
        self.root = Path(root)
        self.root.mkdir(parents=True, exist_ok=True)

    def put_bars(self, symbol: str, freq: str, bars: list[BarRecord]) -> int:
        # Placeholder: write CSV in future; return count
        return len(bars)

    def query_bars(self, symbol: str, freq: str, start: int, end: int) -> list[BarRecord]:
        return []

