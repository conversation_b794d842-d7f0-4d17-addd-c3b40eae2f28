"""
订单路由器 - 多账户订单路由与管理

负责订单的智能路由和管理，包括：
- 多账户订单分发
- 订单拆分与合并
- 订单优先级管理
- 订单执行监控
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import time
import uuid
from collections import defaultdict, deque

from .broker_adapter import BrokerAdapter, Order, OrderSide, OrderType, OrderStatus

logger = logging.getLogger(__name__)


class RoutingStrategy(Enum):
    """路由策略"""
    ROUND_ROBIN = "round_robin"      # 轮询
    LEAST_LOADED = "least_loaded"    # 最少负载
    ACCOUNT_BASED = "account_based"  # 基于账户
    SYMBOL_BASED = "symbol_based"    # 基于标的
    CUSTOM = "custom"                # 自定义


@dataclass
class AccountInfo:
    """账户信息"""
    account_id: str
    broker_adapter: BrokerAdapter
    weight: float = 1.0
    max_orders: int = 100
    current_orders: int = 0
    enabled: bool = True
    last_used: float = 0.0


@dataclass
class RouteRule:
    """路由规则"""
    name: str
    condition: str  # 条件表达式
    target_accounts: List[str]
    priority: int = 0
    enabled: bool = True


@dataclass
class OrderRequest:
    """订单请求"""
    request_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: float = 0.0
    strategy_id: str = ""
    account_hint: str = ""  # 账户提示
    priority: int = 0
    create_time: float = field(default_factory=time.time)
    
    
@dataclass
class RoutedOrder:
    """路由后的订单"""
    request_id: str
    account_id: str
    order_id: str
    quantity: int
    status: OrderStatus = OrderStatus.PENDING
    create_time: float = field(default_factory=time.time)


class OrderRouter:
    """订单路由器"""
    
    def __init__(self, routing_strategy: RoutingStrategy = RoutingStrategy.ROUND_ROBIN):
        """
        初始化订单路由器
        
        Args:
            routing_strategy: 路由策略
        """
        self.routing_strategy = routing_strategy
        
        # 账户管理
        self._accounts: Dict[str, AccountInfo] = {}
        
        # 路由规则
        self._route_rules: List[RouteRule] = []
        
        # 订单队列
        self._order_queue: deque = deque()
        self._pending_orders: Dict[str, OrderRequest] = {}
        self._routed_orders: Dict[str, List[RoutedOrder]] = defaultdict(list)
        
        # 回调函数
        self._order_callbacks: List[Callable[[Order], None]] = []
        
        # 统计信息
        self._stats = {
            'total_requests': 0,
            'routed_orders': 0,
            'failed_routes': 0,
            'completed_orders': 0
        }
        
        # 运行状态
        self._running = False
        self._worker_task: Optional[asyncio.Task] = None
        
    def add_account(self, 
                   account_id: str, 
                   broker_adapter: BrokerAdapter,
                   weight: float = 1.0,
                   max_orders: int = 100) -> None:
        """
        添加账户
        
        Args:
            account_id: 账户ID
            broker_adapter: 交易适配器
            weight: 权重
            max_orders: 最大订单数
        """
        account_info = AccountInfo(
            account_id=account_id,
            broker_adapter=broker_adapter,
            weight=weight,
            max_orders=max_orders
        )
        
        self._accounts[account_id] = account_info
        
        # 注册回调
        broker_adapter.register_order_callback(self._on_order_update)
        
        logger.info(f"Added account: {account_id} with weight {weight}")
        
    def remove_account(self, account_id: str) -> None:
        """
        移除账户
        
        Args:
            account_id: 账户ID
        """
        if account_id in self._accounts:
            del self._accounts[account_id]
            logger.info(f"Removed account: {account_id}")
        
    def add_route_rule(self, rule: RouteRule) -> None:
        """
        添加路由规则
        
        Args:
            rule: 路由规则
        """
        self._route_rules.append(rule)
        # 按优先级排序
        self._route_rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"Added route rule: {rule.name}")
        
    def remove_route_rule(self, rule_name: str) -> None:
        """
        移除路由规则
        
        Args:
            rule_name: 规则名称
        """
        self._route_rules = [r for r in self._route_rules if r.name != rule_name]
        logger.info(f"Removed route rule: {rule_name}")
        
    async def start(self) -> None:
        """启动订单路由器"""
        if self._running:
            logger.warning("Order router is already running")
            return
            
        self._running = True
        self._worker_task = asyncio.create_task(self._order_worker())
        logger.info("Order router started")
        
    async def stop(self) -> None:
        """停止订单路由器"""
        if not self._running:
            return
            
        self._running = False
        
        if self._worker_task:
            self._worker_task.cancel()
            try:
                await self._worker_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Order router stopped")
        
    async def submit_order(self, order_request: OrderRequest) -> str:
        """
        提交订单请求
        
        Args:
            order_request: 订单请求
            
        Returns:
            str: 请求ID
        """
        try:
            if not order_request.request_id:
                order_request.request_id = str(uuid.uuid4())
                
            self._pending_orders[order_request.request_id] = order_request
            self._order_queue.append(order_request)
            self._stats['total_requests'] += 1
            
            logger.info(f"Order request submitted: {order_request.request_id}")
            return order_request.request_id
            
        except Exception as e:
            logger.error(f"Error submitting order request: {e}")
            raise
            
    async def cancel_order(self, request_id: str) -> bool:
        """
        撤销订单
        
        Args:
            request_id: 请求ID
            
        Returns:
            bool: 撤销是否成功
        """
        try:
            routed_orders = self._routed_orders.get(request_id, [])
            success_count = 0
            
            for routed_order in routed_orders:
                if routed_order.status in [OrderStatus.SUBMITTED, OrderStatus.PARTIAL_FILLED]:
                    account_info = self._accounts.get(routed_order.account_id)
                    if account_info:
                        success = await account_info.broker_adapter.cancel_order(routed_order.order_id)
                        if success:
                            success_count += 1
                            
            logger.info(f"Cancelled {success_count} orders for request {request_id}")
            return success_count > 0
            
        except Exception as e:
            logger.error(f"Error cancelling order {request_id}: {e}")
            return False
            
    def _select_accounts(self, order_request: OrderRequest) -> List[str]:
        """
        选择账户
        
        Args:
            order_request: 订单请求
            
        Returns:
            List[str]: 选中的账户ID列表
        """
        # 首先检查路由规则
        for rule in self._route_rules:
            if rule.enabled and self._match_rule(rule, order_request):
                available_accounts = [acc_id for acc_id in rule.target_accounts 
                                    if acc_id in self._accounts and self._accounts[acc_id].enabled]
                if available_accounts:
                    return available_accounts
                    
        # 如果有账户提示，优先使用
        if order_request.account_hint and order_request.account_hint in self._accounts:
            if self._accounts[order_request.account_hint].enabled:
                return [order_request.account_hint]
                
        # 使用默认路由策略
        available_accounts = [acc_id for acc_id, acc_info in self._accounts.items() 
                            if acc_info.enabled and acc_info.current_orders < acc_info.max_orders]
        
        if not available_accounts:
            return []
            
        if self.routing_strategy == RoutingStrategy.ROUND_ROBIN:
            # 轮询策略：选择最久未使用的账户
            return [min(available_accounts, key=lambda x: self._accounts[x].last_used)]
            
        elif self.routing_strategy == RoutingStrategy.LEAST_LOADED:
            # 最少负载策略：选择当前订单数最少的账户
            return [min(available_accounts, key=lambda x: self._accounts[x].current_orders)]
            
        elif self.routing_strategy == RoutingStrategy.ACCOUNT_BASED:
            # 基于账户权重的随机选择
            import random
            weights = [self._accounts[acc_id].weight for acc_id in available_accounts]
            selected = random.choices(available_accounts, weights=weights, k=1)
            return selected
            
        else:
            # 默认返回第一个可用账户
            return [available_accounts[0]]
            
    def _match_rule(self, rule: RouteRule, order_request: OrderRequest) -> bool:
        """
        匹配路由规则
        
        Args:
            rule: 路由规则
            order_request: 订单请求
            
        Returns:
            bool: 是否匹配
        """
        try:
            # 简单的条件匹配实现
            # 实际应用中可以使用更复杂的表达式解析器
            condition = rule.condition.lower()
            
            if "symbol" in condition:
                if order_request.symbol.lower() in condition:
                    return True
                    
            if "side" in condition:
                if order_request.side.value.lower() in condition:
                    return True
                    
            if "strategy" in condition:
                if order_request.strategy_id.lower() in condition:
                    return True
                    
            return False
            
        except Exception as e:
            logger.error(f"Error matching rule {rule.name}: {e}")
            return False
            
    def _split_order(self, order_request: OrderRequest, accounts: List[str]) -> List[Tuple[str, int]]:
        """
        拆分订单
        
        Args:
            order_request: 订单请求
            accounts: 账户列表
            
        Returns:
            List[Tuple[str, int]]: (账户ID, 数量) 列表
        """
        if len(accounts) == 1:
            return [(accounts[0], order_request.quantity)]
            
        # 按权重分配数量
        total_weight = sum(self._accounts[acc_id].weight for acc_id in accounts)
        splits = []
        remaining_quantity = order_request.quantity
        
        for i, account_id in enumerate(accounts):
            if i == len(accounts) - 1:
                # 最后一个账户分配剩余数量
                splits.append((account_id, remaining_quantity))
            else:
                weight = self._accounts[account_id].weight
                quantity = int(order_request.quantity * weight / total_weight)
                quantity = min(quantity, remaining_quantity)
                if quantity > 0:
                    splits.append((account_id, quantity))
                    remaining_quantity -= quantity
                    
        return [split for split in splits if split[1] > 0]
        
    async def _process_order_request(self, order_request: OrderRequest) -> None:
        """
        处理订单请求
        
        Args:
            order_request: 订单请求
        """
        try:
            # 选择账户
            selected_accounts = self._select_accounts(order_request)
            if not selected_accounts:
                logger.error(f"No available accounts for order {order_request.request_id}")
                self._stats['failed_routes'] += 1
                return
                
            # 拆分订单
            order_splits = self._split_order(order_request, selected_accounts)
            
            # 提交订单到各个账户
            for account_id, quantity in order_splits:
                account_info = self._accounts[account_id]
                
                # 提交订单
                order_id = await account_info.broker_adapter.place_order(
                    symbol=order_request.symbol,
                    side=order_request.side,
                    order_type=order_request.order_type,
                    quantity=quantity,
                    price=order_request.price
                )
                
                if order_id:
                    # 创建路由订单记录
                    routed_order = RoutedOrder(
                        request_id=order_request.request_id,
                        account_id=account_id,
                        order_id=order_id,
                        quantity=quantity,
                        status=OrderStatus.SUBMITTED
                    )
                    
                    self._routed_orders[order_request.request_id].append(routed_order)
                    
                    # 更新账户状态
                    account_info.current_orders += 1
                    account_info.last_used = time.time()
                    
                    self._stats['routed_orders'] += 1
                    logger.info(f"Routed order {order_id} to account {account_id}")
                else:
                    logger.error(f"Failed to route order to account {account_id}")
                    self._stats['failed_routes'] += 1
                    
        except Exception as e:
            logger.error(f"Error processing order request {order_request.request_id}: {e}")
            self._stats['failed_routes'] += 1
            
    async def _order_worker(self) -> None:
        """订单处理工作线程"""
        logger.info("Order worker started")
        
        while self._running:
            try:
                if self._order_queue:
                    order_request = self._order_queue.popleft()
                    await self._process_order_request(order_request)
                else:
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Error in order worker: {e}")
                
        logger.info("Order worker stopped")
        
    def _on_order_update(self, order: Order) -> None:
        """订单更新回调"""
        try:
            # 更新路由订单状态
            for request_id, routed_orders in self._routed_orders.items():
                for routed_order in routed_orders:
                    if routed_order.order_id == order.order_id:
                        routed_order.status = order.status
                        
                        # 如果订单完成，减少账户订单计数
                        if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                            account_info = self._accounts.get(routed_order.account_id)
                            if account_info:
                                account_info.current_orders = max(0, account_info.current_orders - 1)
                                
                            if order.status == OrderStatus.FILLED:
                                self._stats['completed_orders'] += 1
                        break
                        
            # 调用注册的回调函数
            for callback in self._order_callbacks:
                try:
                    callback(order)
                except Exception as e:
                    logger.error(f"Error in order callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing order update: {e}")
            
    def register_order_callback(self, callback: Callable[[Order], None]) -> None:
        """注册订单回调"""
        self._order_callbacks.append(callback)
        
    def get_order_status(self, request_id: str) -> Dict[str, Any]:
        """获取订单状态"""
        routed_orders = self._routed_orders.get(request_id, [])
        
        total_quantity = sum(ro.quantity for ro in routed_orders)
        filled_quantity = sum(ro.quantity for ro in routed_orders if ro.status == OrderStatus.FILLED)
        
        return {
            'request_id': request_id,
            'total_quantity': total_quantity,
            'filled_quantity': filled_quantity,
            'routed_orders': len(routed_orders),
            'status': 'completed' if filled_quantity == total_quantity else 'pending'
        }
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        stats.update({
            'accounts': len(self._accounts),
            'active_accounts': len([a for a in self._accounts.values() if a.enabled]),
            'pending_requests': len(self._order_queue),
            'route_rules': len(self._route_rules)
        })
        return stats
