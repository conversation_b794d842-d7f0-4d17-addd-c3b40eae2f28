"""
风险控制引擎 - 多层级风险管理

负责交易风险的实时监控和控制，包括：
- 前置风控检查
- 实时风险监控
- 风险指标计算
- 风险事件处理
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import time
from collections import defaultdict, deque

from .broker_adapter import Order, OrderSide, OrderType, Position, Account
from .order_router import OrderRequest

logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险级别"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskAction(Enum):
    """风险处理动作"""
    ALLOW = "allow"          # 允许
    WARNING = "warning"      # 警告
    REJECT = "reject"        # 拒绝
    REDUCE = "reduce"        # 减量
    CANCEL = "cancel"        # 撤单
    STOP_TRADING = "stop_trading"  # 停止交易


@dataclass
class RiskRule:
    """风险规则"""
    name: str
    description: str
    rule_type: str  # position, order, account, market
    condition: str  # 条件表达式
    threshold: float
    action: RiskAction
    enabled: bool = True
    priority: int = 0


@dataclass
class RiskEvent:
    """风险事件"""
    event_id: str
    rule_name: str
    risk_level: RiskLevel
    message: str
    data: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)
    handled: bool = False


@dataclass
class RiskMetrics:
    """风险指标"""
    account_id: str
    timestamp: float
    
    # 资金风险
    total_asset: float = 0.0
    available_cash: float = 0.0
    used_margin: float = 0.0
    margin_ratio: float = 0.0
    
    # 持仓风险
    total_position_value: float = 0.0
    max_single_position_ratio: float = 0.0
    position_concentration: float = 0.0
    
    # 交易风险
    daily_pnl: float = 0.0
    daily_pnl_ratio: float = 0.0
    max_drawdown: float = 0.0
    order_count: int = 0
    order_frequency: float = 0.0
    
    # 市场风险
    beta: float = 0.0
    var: float = 0.0  # Value at Risk
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


class RiskEngine:
    """风险控制引擎"""
    
    def __init__(self):
        """初始化风险引擎"""
        # 风险规则
        self._risk_rules: List[RiskRule] = []
        
        # 风险事件
        self._risk_events: deque = deque(maxlen=1000)
        self._event_callbacks: List[Callable[[RiskEvent], None]] = []
        
        # 风险指标
        self._risk_metrics: Dict[str, RiskMetrics] = {}
        
        # 账户数据缓存
        self._accounts: Dict[str, Account] = {}
        self._positions: Dict[str, List[Position]] = defaultdict(list)
        self._orders: Dict[str, List[Order]] = defaultdict(list)
        
        # 价格数据缓存
        self._prices: Dict[str, float] = {}
        
        # 统计信息
        self._stats = {
            'total_checks': 0,
            'rejected_orders': 0,
            'risk_events': 0,
            'rules_triggered': 0
        }
        
        # 运行状态
        self._running = False
        self._monitor_task: Optional[asyncio.Task] = None
        
        # 初始化默认规则
        self._init_default_rules()
        
    def _init_default_rules(self) -> None:
        """初始化默认风险规则"""
        default_rules = [
            RiskRule(
                name="max_position_ratio",
                description="单一持仓比例限制",
                rule_type="position",
                condition="single_position_ratio > threshold",
                threshold=0.1,  # 10%
                action=RiskAction.REJECT,
                priority=1
            ),
            RiskRule(
                name="max_daily_loss",
                description="日内最大亏损限制",
                rule_type="account",
                condition="daily_pnl_ratio < -threshold",
                threshold=0.05,  # -5%
                action=RiskAction.STOP_TRADING,
                priority=1
            ),
            RiskRule(
                name="margin_ratio_limit",
                description="保证金比例限制",
                rule_type="account",
                condition="margin_ratio > threshold",
                threshold=0.95,  # 95%
                action=RiskAction.REJECT,
                priority=1
            ),
            RiskRule(
                name="order_frequency_limit",
                description="订单频率限制",
                rule_type="order",
                condition="order_frequency > threshold",
                threshold=10,  # 每秒10单
                action=RiskAction.REJECT,
                priority=2
            ),
            RiskRule(
                name="max_drawdown_limit",
                description="最大回撤限制",
                rule_type="account",
                condition="max_drawdown > threshold",
                threshold=0.15,  # 15%
                action=RiskAction.WARNING,
                priority=2
            )
        ]
        
        self._risk_rules.extend(default_rules)
        logger.info(f"Initialized {len(default_rules)} default risk rules")
        
    def add_risk_rule(self, rule: RiskRule) -> None:
        """
        添加风险规则
        
        Args:
            rule: 风险规则
        """
        self._risk_rules.append(rule)
        # 按优先级排序
        self._risk_rules.sort(key=lambda x: x.priority, reverse=True)
        logger.info(f"Added risk rule: {rule.name}")
        
    def remove_risk_rule(self, rule_name: str) -> None:
        """
        移除风险规则
        
        Args:
            rule_name: 规则名称
        """
        self._risk_rules = [r for r in self._risk_rules if r.name != rule_name]
        logger.info(f"Removed risk rule: {rule_name}")
        
    def enable_rule(self, rule_name: str, enabled: bool = True) -> None:
        """
        启用/禁用风险规则
        
        Args:
            rule_name: 规则名称
            enabled: 是否启用
        """
        for rule in self._risk_rules:
            if rule.name == rule_name:
                rule.enabled = enabled
                logger.info(f"Rule {rule_name} {'enabled' if enabled else 'disabled'}")
                break
                
    async def start(self) -> None:
        """启动风险引擎"""
        if self._running:
            logger.warning("Risk engine is already running")
            return
            
        self._running = True
        self._monitor_task = asyncio.create_task(self._risk_monitor())
        logger.info("Risk engine started")
        
    async def stop(self) -> None:
        """停止风险引擎"""
        if not self._running:
            return
            
        self._running = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
                
        logger.info("Risk engine stopped")
        
    async def check_order_risk(self, order_request: OrderRequest, account_id: str) -> RiskAction:
        """
        检查订单风险
        
        Args:
            order_request: 订单请求
            account_id: 账户ID
            
        Returns:
            RiskAction: 风险处理动作
        """
        try:
            self._stats['total_checks'] += 1
            
            # 更新风险指标
            await self._update_risk_metrics(account_id)
            
            # 检查所有风险规则
            for rule in self._risk_rules:
                if not rule.enabled:
                    continue
                    
                action = await self._check_rule(rule, order_request, account_id)
                if action != RiskAction.ALLOW:
                    # 触发风险规则
                    await self._handle_risk_event(rule, action, {
                        'order_request': order_request,
                        'account_id': account_id
                    })
                    
                    if action in [RiskAction.REJECT, RiskAction.STOP_TRADING]:
                        self._stats['rejected_orders'] += 1
                        return action
                        
            return RiskAction.ALLOW
            
        except Exception as e:
            logger.error(f"Error checking order risk: {e}")
            return RiskAction.REJECT
            
    async def _check_rule(self, rule: RiskRule, order_request: OrderRequest, account_id: str) -> RiskAction:
        """
        检查单个风险规则
        
        Args:
            rule: 风险规则
            order_request: 订单请求
            account_id: 账户ID
            
        Returns:
            RiskAction: 风险处理动作
        """
        try:
            metrics = self._risk_metrics.get(account_id)
            if not metrics:
                return RiskAction.ALLOW
                
            # 根据规则类型进行检查
            if rule.rule_type == "position":
                return await self._check_position_rule(rule, order_request, account_id, metrics)
            elif rule.rule_type == "account":
                return await self._check_account_rule(rule, order_request, account_id, metrics)
            elif rule.rule_type == "order":
                return await self._check_order_rule(rule, order_request, account_id, metrics)
            elif rule.rule_type == "market":
                return await self._check_market_rule(rule, order_request, account_id, metrics)
            else:
                return RiskAction.ALLOW
                
        except Exception as e:
            logger.error(f"Error checking rule {rule.name}: {e}")
            return RiskAction.ALLOW
            
    async def _check_position_rule(self, rule: RiskRule, order_request: OrderRequest, 
                                 account_id: str, metrics: RiskMetrics) -> RiskAction:
        """检查持仓风险规则"""
        if rule.name == "max_position_ratio":
            # 计算新订单后的持仓比例
            current_price = self._prices.get(order_request.symbol, 0)
            if current_price <= 0:
                return RiskAction.ALLOW
                
            order_value = order_request.quantity * current_price
            new_position_ratio = order_value / max(metrics.total_asset, 1)
            
            if new_position_ratio > rule.threshold:
                return rule.action
                
        return RiskAction.ALLOW
        
    async def _check_account_rule(self, rule: RiskRule, order_request: OrderRequest,
                                account_id: str, metrics: RiskMetrics) -> RiskAction:
        """检查账户风险规则"""
        if rule.name == "max_daily_loss":
            if metrics.daily_pnl_ratio < -rule.threshold:
                return rule.action
                
        elif rule.name == "margin_ratio_limit":
            if metrics.margin_ratio > rule.threshold:
                return rule.action
                
        elif rule.name == "max_drawdown_limit":
            if metrics.max_drawdown > rule.threshold:
                return rule.action
                
        return RiskAction.ALLOW
        
    async def _check_order_rule(self, rule: RiskRule, order_request: OrderRequest,
                              account_id: str, metrics: RiskMetrics) -> RiskAction:
        """检查订单风险规则"""
        if rule.name == "order_frequency_limit":
            if metrics.order_frequency > rule.threshold:
                return rule.action
                
        return RiskAction.ALLOW
        
    async def _check_market_rule(self, rule: RiskRule, order_request: OrderRequest,
                               account_id: str, metrics: RiskMetrics) -> RiskAction:
        """检查市场风险规则"""
        # 实现市场风险检查逻辑
        return RiskAction.ALLOW
        
    async def _update_risk_metrics(self, account_id: str) -> None:
        """
        更新风险指标
        
        Args:
            account_id: 账户ID
        """
        try:
            account = self._accounts.get(account_id)
            positions = self._positions.get(account_id, [])
            orders = self._orders.get(account_id, [])
            
            if not account:
                return
                
            metrics = RiskMetrics(account_id=account_id, timestamp=time.time())
            
            # 资金风险指标
            metrics.total_asset = account.total_asset
            metrics.available_cash = account.available_cash
            metrics.used_margin = account.total_asset - account.available_cash
            metrics.margin_ratio = metrics.used_margin / max(metrics.total_asset, 1)
            
            # 持仓风险指标
            metrics.total_position_value = sum(pos.market_value for pos in positions)
            if positions:
                max_position_value = max(pos.market_value for pos in positions)
                metrics.max_single_position_ratio = max_position_value / max(metrics.total_asset, 1)
                
            # 交易风险指标
            metrics.daily_pnl = account.total_pnl
            metrics.daily_pnl_ratio = metrics.daily_pnl / max(metrics.total_asset, 1)
            
            # 订单频率
            recent_orders = [o for o in orders if time.time() - time.mktime(time.strptime(o.create_time, "%Y%m%d %H:%M:%S")) < 60]
            metrics.order_count = len(recent_orders)
            metrics.order_frequency = len(recent_orders) / 60.0  # 每秒订单数
            
            self._risk_metrics[account_id] = metrics
            
        except Exception as e:
            logger.error(f"Error updating risk metrics for {account_id}: {e}")
            
    async def _handle_risk_event(self, rule: RiskRule, action: RiskAction, data: Dict[str, Any]) -> None:
        """
        处理风险事件
        
        Args:
            rule: 触发的规则
            action: 处理动作
            data: 事件数据
        """
        try:
            # 确定风险级别
            risk_level = RiskLevel.LOW
            if action == RiskAction.WARNING:
                risk_level = RiskLevel.MEDIUM
            elif action == RiskAction.REJECT:
                risk_level = RiskLevel.HIGH
            elif action == RiskAction.STOP_TRADING:
                risk_level = RiskLevel.CRITICAL
                
            # 创建风险事件
            event = RiskEvent(
                event_id=str(time.time()),
                rule_name=rule.name,
                risk_level=risk_level,
                message=f"Risk rule '{rule.name}' triggered: {rule.description}",
                data=data
            )
            
            self._risk_events.append(event)
            self._stats['risk_events'] += 1
            self._stats['rules_triggered'] += 1
            
            # 调用事件回调
            for callback in self._event_callbacks:
                try:
                    callback(event)
                except Exception as e:
                    logger.error(f"Error in risk event callback: {e}")
                    
            logger.warning(f"Risk event: {event.message}")
            
        except Exception as e:
            logger.error(f"Error handling risk event: {e}")
            
    async def _risk_monitor(self) -> None:
        """风险监控任务"""
        logger.info("Risk monitor started")
        
        while self._running:
            try:
                # 定期更新所有账户的风险指标
                for account_id in self._accounts.keys():
                    await self._update_risk_metrics(account_id)
                    
                await asyncio.sleep(5)  # 每5秒更新一次
                
            except Exception as e:
                logger.error(f"Error in risk monitor: {e}")
                
        logger.info("Risk monitor stopped")
        
    def update_account(self, account: Account) -> None:
        """更新账户信息"""
        self._accounts[account.account_id] = account
        
    def update_positions(self, account_id: str, positions: List[Position]) -> None:
        """更新持仓信息"""
        self._positions[account_id] = positions
        
    def update_orders(self, account_id: str, orders: List[Order]) -> None:
        """更新订单信息"""
        self._orders[account_id] = orders
        
    def update_price(self, symbol: str, price: float) -> None:
        """更新价格信息"""
        self._prices[symbol] = price
        
    def register_event_callback(self, callback: Callable[[RiskEvent], None]) -> None:
        """注册风险事件回调"""
        self._event_callbacks.append(callback)
        
    def get_risk_metrics(self, account_id: str) -> Optional[RiskMetrics]:
        """获取风险指标"""
        return self._risk_metrics.get(account_id)
        
    def get_recent_events(self, limit: int = 10) -> List[RiskEvent]:
        """获取最近的风险事件"""
        return list(self._risk_events)[-limit:]
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self._stats.copy()
        stats.update({
            'active_rules': len([r for r in self._risk_rules if r.enabled]),
            'total_rules': len(self._risk_rules),
            'monitored_accounts': len(self._accounts),
            'recent_events': len(self._risk_events)
        })
        return stats
