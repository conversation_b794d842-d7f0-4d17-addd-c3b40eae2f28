"""
投资组合管理器 - 多账户投资组合管理

负责投资组合的管理和分析，包括：
- 多账户投资组合统计
- 持仓分析与归因
- 盈亏计算与分析
- 风险指标计算
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, date
import pandas as pd
import numpy as np
from collections import defaultdict
import time

from .broker_adapter import Account, Position, Trade, Order

logger = logging.getLogger(__name__)


@dataclass
class PortfolioSummary:
    """投资组合摘要"""
    account_id: str
    timestamp: float
    
    # 资产信息
    total_asset: float = 0.0
    available_cash: float = 0.0
    market_value: float = 0.0
    
    # 盈亏信息
    total_pnl: float = 0.0
    realized_pnl: float = 0.0
    unrealized_pnl: float = 0.0
    
    # 收益率
    total_return: float = 0.0
    daily_return: float = 0.0
    
    # 持仓统计
    position_count: int = 0
    long_positions: int = 0
    short_positions: int = 0
    
    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()


@dataclass
class PositionAnalysis:
    """持仓分析"""
    symbol: str
    quantity: int
    avg_cost: float
    current_price: float
    market_value: float
    unrealized_pnl: float
    unrealized_pnl_ratio: float
    weight: float  # 在组合中的权重
    
    # 风险指标
    beta: float = 0.0
    volatility: float = 0.0
    var: float = 0.0  # Value at Risk
    
    # 行业/概念分类
    industry: str = ""
    concepts: List[str] = field(default_factory=list)


@dataclass
class PerformanceMetrics:
    """业绩指标"""
    account_id: str
    period: str  # daily, weekly, monthly, yearly
    
    # 收益指标
    total_return: float = 0.0
    annualized_return: float = 0.0
    excess_return: float = 0.0
    
    # 风险指标
    volatility: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0
    
    # 风险调整收益
    sharpe_ratio: float = 0.0
    calmar_ratio: float = 0.0
    sortino_ratio: float = 0.0
    
    # 交易统计
    win_rate: float = 0.0
    profit_loss_ratio: float = 0.0
    trade_count: int = 0


class Portfolio:
    """投资组合管理器"""
    
    def __init__(self):
        """初始化投资组合管理器"""
        # 账户数据
        self._accounts: Dict[str, Account] = {}
        self._positions: Dict[str, List[Position]] = defaultdict(list)
        self._trades: Dict[str, List[Trade]] = defaultdict(list)
        self._orders: Dict[str, List[Order]] = defaultdict(list)
        
        # 价格数据
        self._prices: Dict[str, float] = {}
        self._price_history: Dict[str, List[Tuple[float, float]]] = defaultdict(list)  # (timestamp, price)
        
        # 基准数据
        self._benchmark_prices: Dict[str, float] = {}
        self._benchmark_history: Dict[str, List[Tuple[float, float]]] = defaultdict(list)
        
        # 历史净值
        self._nav_history: Dict[str, List[Tuple[float, float]]] = defaultdict(list)  # (timestamp, nav)
        
        # 行业/概念数据
        self._symbol_industry: Dict[str, str] = {}
        self._symbol_concepts: Dict[str, List[str]] = defaultdict(list)
        
        # 统计信息
        self._stats = {
            'total_accounts': 0,
            'total_positions': 0,
            'total_trades': 0,
            'last_update': 0
        }
        
    def update_account(self, account: Account) -> None:
        """
        更新账户信息
        
        Args:
            account: 账户信息
        """
        self._accounts[account.account_id] = account
        self._stats['last_update'] = time.time()
        
        # 更新净值历史
        nav = account.total_asset
        timestamp = time.time()
        self._nav_history[account.account_id].append((timestamp, nav))
        
        # 保留最近1000个点
        if len(self._nav_history[account.account_id]) > 1000:
            self._nav_history[account.account_id] = self._nav_history[account.account_id][-1000:]
            
    def update_positions(self, account_id: str, positions: List[Position]) -> None:
        """
        更新持仓信息
        
        Args:
            account_id: 账户ID
            positions: 持仓列表
        """
        self._positions[account_id] = positions
        self._stats['total_positions'] = sum(len(pos) for pos in self._positions.values())
        
    def update_trades(self, account_id: str, trades: List[Trade]) -> None:
        """
        更新成交信息
        
        Args:
            account_id: 账户ID
            trades: 成交列表
        """
        self._trades[account_id] = trades
        self._stats['total_trades'] = sum(len(trades) for trades in self._trades.values())
        
    def update_orders(self, account_id: str, orders: List[Order]) -> None:
        """
        更新订单信息
        
        Args:
            account_id: 账户ID
            orders: 订单列表
        """
        self._orders[account_id] = orders
        
    def update_price(self, symbol: str, price: float) -> None:
        """
        更新价格信息
        
        Args:
            symbol: 股票代码
            price: 价格
        """
        self._prices[symbol] = price
        timestamp = time.time()
        self._price_history[symbol].append((timestamp, price))
        
        # 保留最近1000个点
        if len(self._price_history[symbol]) > 1000:
            self._price_history[symbol] = self._price_history[symbol][-1000:]
            
    def update_benchmark(self, benchmark: str, price: float) -> None:
        """
        更新基准价格
        
        Args:
            benchmark: 基准代码
            price: 价格
        """
        self._benchmark_prices[benchmark] = price
        timestamp = time.time()
        self._benchmark_history[benchmark].append((timestamp, price))
        
        # 保留最近1000个点
        if len(self._benchmark_history[benchmark]) > 1000:
            self._benchmark_history[benchmark] = self._benchmark_history[benchmark][-1000:]
            
    def set_symbol_metadata(self, symbol: str, industry: str = "", concepts: List[str] = None) -> None:
        """
        设置股票元数据
        
        Args:
            symbol: 股票代码
            industry: 行业
            concepts: 概念列表
        """
        if industry:
            self._symbol_industry[symbol] = industry
        if concepts:
            self._symbol_concepts[symbol] = concepts
            
    def get_portfolio_summary(self, account_id: str) -> Optional[PortfolioSummary]:
        """
        获取投资组合摘要
        
        Args:
            account_id: 账户ID
            
        Returns:
            Optional[PortfolioSummary]: 投资组合摘要
        """
        try:
            account = self._accounts.get(account_id)
            if not account:
                return None
                
            positions = self._positions.get(account_id, [])
            
            summary = PortfolioSummary(
                account_id=account_id,
                timestamp=time.time(),
                total_asset=account.total_asset,
                available_cash=account.available_cash,
                market_value=account.market_value,
                total_pnl=account.total_pnl
            )
            
            # 计算持仓统计
            summary.position_count = len(positions)
            summary.long_positions = len([p for p in positions if p.quantity > 0])
            summary.short_positions = len([p for p in positions if p.quantity < 0])
            
            # 计算盈亏
            summary.realized_pnl = sum(p.realized_pnl for p in positions)
            summary.unrealized_pnl = sum(p.unrealized_pnl for p in positions)
            
            # 计算收益率
            nav_history = self._nav_history.get(account_id, [])
            if len(nav_history) >= 2:
                initial_nav = nav_history[0][1]
                current_nav = nav_history[-1][1]
                summary.total_return = (current_nav - initial_nav) / initial_nav
                
                # 日收益率
                if len(nav_history) >= 2:
                    prev_nav = nav_history[-2][1]
                    summary.daily_return = (current_nav - prev_nav) / prev_nav
                    
            return summary

        except Exception as e:
            logger.error(f"Error getting portfolio summary for {account_id}: {e}")
            return None

    def get_position_analysis(self, account_id: str) -> List[PositionAnalysis]:
        """
        获取持仓分析

        Args:
            account_id: 账户ID

        Returns:
            List[PositionAnalysis]: 持仓分析列表
        """
        try:
            positions = self._positions.get(account_id, [])
            account = self._accounts.get(account_id)

            if not account:
                return []

            analyses = []
            total_asset = account.total_asset

            for position in positions:
                if position.quantity == 0:
                    continue

                current_price = self._prices.get(position.symbol, 0)
                if current_price <= 0:
                    current_price = position.market_value / abs(position.quantity) if position.quantity != 0 else 0

                market_value = position.quantity * current_price
                unrealized_pnl = market_value - position.quantity * position.avg_cost
                unrealized_pnl_ratio = unrealized_pnl / (position.quantity * position.avg_cost) if position.avg_cost > 0 else 0
                weight = abs(market_value) / total_asset if total_asset > 0 else 0

                # 计算风险指标
                volatility = self._calculate_volatility(position.symbol)
                beta = self._calculate_beta(position.symbol)
                var = self._calculate_var(position.symbol, market_value)

                analysis = PositionAnalysis(
                    symbol=position.symbol,
                    quantity=position.quantity,
                    avg_cost=position.avg_cost,
                    current_price=current_price,
                    market_value=market_value,
                    unrealized_pnl=unrealized_pnl,
                    unrealized_pnl_ratio=unrealized_pnl_ratio,
                    weight=weight,
                    beta=beta,
                    volatility=volatility,
                    var=var,
                    industry=self._symbol_industry.get(position.symbol, ""),
                    concepts=self._symbol_concepts.get(position.symbol, [])
                )

                analyses.append(analysis)

            # 按权重排序
            analyses.sort(key=lambda x: abs(x.weight), reverse=True)
            return analyses

        except Exception as e:
            logger.error(f"Error getting position analysis for {account_id}: {e}")
            return []

    def calculate_performance_metrics(self, account_id: str, period: str = "daily") -> Optional[PerformanceMetrics]:
        """
        计算业绩指标

        Args:
            account_id: 账户ID
            period: 计算周期

        Returns:
            Optional[PerformanceMetrics]: 业绩指标
        """
        try:
            nav_history = self._nav_history.get(account_id, [])
            if len(nav_history) < 2:
                return None

            # 转换为DataFrame便于计算
            df = pd.DataFrame(nav_history, columns=['timestamp', 'nav'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df.set_index('timestamp', inplace=True)

            # 计算收益率
            df['returns'] = df['nav'].pct_change().dropna()

            if df['returns'].empty:
                return None

            metrics = PerformanceMetrics(account_id=account_id, period=period)

            # 总收益率
            metrics.total_return = (df['nav'].iloc[-1] - df['nav'].iloc[0]) / df['nav'].iloc[0]

            # 年化收益率
            days = (df.index[-1] - df.index[0]).days
            if days > 0:
                metrics.annualized_return = (1 + metrics.total_return) ** (365 / days) - 1

            # 波动率
            metrics.volatility = df['returns'].std() * np.sqrt(252)  # 年化波动率

            # 最大回撤
            cumulative = (1 + df['returns']).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            metrics.max_drawdown = abs(drawdown.min())

            # VaR (95%)
            metrics.var_95 = abs(df['returns'].quantile(0.05))

            # 夏普比率 (假设无风险利率为3%)
            risk_free_rate = 0.03
            if metrics.volatility > 0:
                metrics.sharpe_ratio = (metrics.annualized_return - risk_free_rate) / metrics.volatility

            # 卡尔玛比率
            if metrics.max_drawdown > 0:
                metrics.calmar_ratio = metrics.annualized_return / metrics.max_drawdown

            # 索提诺比率
            downside_returns = df['returns'][df['returns'] < 0]
            if len(downside_returns) > 0:
                downside_volatility = downside_returns.std() * np.sqrt(252)
                if downside_volatility > 0:
                    metrics.sortino_ratio = (metrics.annualized_return - risk_free_rate) / downside_volatility

            # 交易统计
            trades = self._trades.get(account_id, [])
            if trades:
                metrics.trade_count = len(trades)

                # 计算胜率和盈亏比
                profitable_trades = [t for t in trades if self._calculate_trade_pnl(t) > 0]
                losing_trades = [t for t in trades if self._calculate_trade_pnl(t) < 0]

                metrics.win_rate = len(profitable_trades) / len(trades) if trades else 0

                if profitable_trades and losing_trades:
                    avg_profit = np.mean([self._calculate_trade_pnl(t) for t in profitable_trades])
                    avg_loss = abs(np.mean([self._calculate_trade_pnl(t) for t in losing_trades]))
                    metrics.profit_loss_ratio = avg_profit / avg_loss if avg_loss > 0 else 0

            return metrics

        except Exception as e:
            logger.error(f"Error calculating performance metrics for {account_id}: {e}")
            return None

    def _calculate_volatility(self, symbol: str, days: int = 30) -> float:
        """计算股票波动率"""
        try:
            price_history = self._price_history.get(symbol, [])
            if len(price_history) < 2:
                return 0.0

            prices = [p[1] for p in price_history[-days:]]
            if len(prices) < 2:
                return 0.0

            returns = [np.log(prices[i] / prices[i-1]) for i in range(1, len(prices))]
            return np.std(returns) * np.sqrt(252) if returns else 0.0

        except Exception as e:
            logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.0

    def _calculate_beta(self, symbol: str, benchmark: str = "000300.SH") -> float:
        """计算股票Beta值"""
        try:
            symbol_history = self._price_history.get(symbol, [])
            benchmark_history = self._benchmark_history.get(benchmark, [])

            if len(symbol_history) < 30 or len(benchmark_history) < 30:
                return 1.0

            # 对齐时间序列
            symbol_prices = dict(symbol_history[-30:])
            benchmark_prices = dict(benchmark_history[-30:])

            common_times = set(symbol_prices.keys()) & set(benchmark_prices.keys())
            if len(common_times) < 10:
                return 1.0

            times = sorted(common_times)
            symbol_values = [symbol_prices[t] for t in times]
            benchmark_values = [benchmark_prices[t] for t in times]

            # 计算收益率
            symbol_returns = [np.log(symbol_values[i] / symbol_values[i-1]) for i in range(1, len(symbol_values))]
            benchmark_returns = [np.log(benchmark_values[i] / benchmark_values[i-1]) for i in range(1, len(benchmark_values))]

            if len(symbol_returns) < 5:
                return 1.0

            # 计算Beta
            covariance = np.cov(symbol_returns, benchmark_returns)[0][1]
            benchmark_variance = np.var(benchmark_returns)

            return covariance / benchmark_variance if benchmark_variance > 0 else 1.0

        except Exception as e:
            logger.error(f"Error calculating beta for {symbol}: {e}")
            return 1.0

    def _calculate_var(self, symbol: str, position_value: float, confidence: float = 0.95) -> float:
        """计算VaR"""
        try:
            volatility = self._calculate_volatility(symbol)
            if volatility <= 0:
                return 0.0

            # 使用正态分布假设计算VaR
            from scipy.stats import norm
            var = abs(position_value * norm.ppf(1 - confidence) * volatility / np.sqrt(252))
            return var

        except Exception as e:
            logger.error(f"Error calculating VaR for {symbol}: {e}")
            return 0.0

    def _calculate_trade_pnl(self, trade: Trade) -> float:
        """计算单笔交易盈亏"""
        # 简化实现，实际需要考虑买卖配对
        return 0.0

    def get_industry_allocation(self, account_id: str) -> Dict[str, float]:
        """获取行业配置"""
        try:
            analyses = self.get_position_analysis(account_id)
            industry_weights = defaultdict(float)

            for analysis in analyses:
                if analysis.industry:
                    industry_weights[analysis.industry] += analysis.weight
                else:
                    industry_weights["其他"] += analysis.weight

            return dict(industry_weights)

        except Exception as e:
            logger.error(f"Error getting industry allocation for {account_id}: {e}")
            return {}

    def get_concept_allocation(self, account_id: str) -> Dict[str, float]:
        """获取概念配置"""
        try:
            analyses = self.get_position_analysis(account_id)
            concept_weights = defaultdict(float)

            for analysis in analyses:
                if analysis.concepts:
                    for concept in analysis.concepts:
                        concept_weights[concept] += analysis.weight / len(analysis.concepts)
                else:
                    concept_weights["其他"] += analysis.weight

            return dict(concept_weights)

        except Exception as e:
            logger.error(f"Error getting concept allocation for {account_id}: {e}")
            return {}

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        self._stats['total_accounts'] = len(self._accounts)
        return self._stats.copy()
