from pathlib import Path
try:
    from fastapi import APIRouter
    from pydantic import BaseModel
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    BaseModel = object  # type: ignore

from app.core.wtpy_integration import LiveFacade

router = APIRouter(prefix="/live", tags=["live"]) if APIRouter else None


class LiveRequest(BaseModel):  # type: ignore[misc]
    config_path: str | None = None


if router:
    @router.post("/run")
    async def run_live(req: LiveRequest):
        cfg = req.config_path or str(Path("app/config/engine.config.json").resolve())
        fac = LiveFacade()
        result = fac.run(config_path=cfg)
        return {"ok": result.ok, "message": result.message}

