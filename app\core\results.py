"""
Backtest results parser: tries to locate and parse an equity curve from an output directory.
This avoids third-party deps; supports common CSV/JSON layouts.
"""
from __future__ import annotations
from pathlib import Path
from typing import List, Tuple
import csv
import json


def _read_csv_series(path: Path) -> list[tuple[str, float]]:
    series: list[tuple[str, float]] = []
    try:
        with path.open("r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            headers = [h.lower() for h in reader.fieldnames or []]
            # Try common columns
            ts_key = None
            for k in ("datetime", "date", "time", "ts", "timestamp"):
                if k in headers:
                    ts_key = k
                    break
            val_key = None
            for k in ("equity", "netvalue", "nav", "close", "value", "val"):
                if k in headers:
                    val_key = k
                    break
            for row in reader:
                if ts_key is None:
                    # fallback: row number
                    ts = str(row.get(headers[0], ""))
                else:
                    # map original header to found lowercase
                    for orig in row.keys():
                        if orig.lower() == ts_key:
                            ts = str(row.get(orig, ""))
                            break
                    else:
                        ts = ""
                if val_key is None:
                    # try the last column
                    try:
                        v = float(list(row.values())[-1])
                    except Exception:
                        continue
                else:
                    v = None
                    for orig in row.keys():
                        if orig.lower() == val_key:
                            try:
                                v = float(row.get(orig, "nan"))
                            except Exception:
                                v = None
                            break
                    if v is None:
                        continue
                series.append((ts, float(v)))
    except Exception:
        return []
    return series


def _read_json_series(path: Path) -> list[tuple[str, float]]:
    try:
        obj = json.loads(path.read_text(encoding="utf-8"))
    except Exception:
        return []
    # try formats: {"equity":[{"ts":...,"value":...}, ...]} or [{"ts":...,"value":...}]
    cand = None
    if isinstance(obj, dict):
        for k in ("equity", "netvalue", "nav", "curve"):
            if isinstance(obj.get(k), list):
                cand = obj.get(k)
                break
    elif isinstance(obj, list):
        cand = obj
    if not isinstance(cand, list):
        return []
    series: list[tuple[str, float]] = []
    for item in cand:
        if not isinstance(item, dict):
            continue
        ts = None
        for k in ("ts", "timestamp", "time", "date", "datetime"):
            if k in item:
                ts = str(item[k])
                break
        val = None
        for k in ("value", "equity", "netvalue", "nav", "close"):
            if k in item:
                try:
                    val = float(item[k])
                except Exception:
                    val = None
                break
        if ts is not None and val is not None:
            series.append((ts, val))
    return series


def parse_equity(out_dir: str | Path) -> List[Tuple[str, float]]:
    outp = Path(out_dir)
    if not outp.exists() or not outp.is_dir():
        return []
    # Candidates to try in priority
    csv_candidates = [
        "equity.csv",
        "netvalue.csv",
        "funds.csv",
        "nav.csv",
    ]
    json_candidates = [
        "btReport.json",
        "report.json",
        "equity.json",
    ]

    # Search recursively but shallow (depth 2)
    paths: list[Path] = []
    for cand in csv_candidates + json_candidates:
        for p in outp.rglob(cand):
            if p.is_file():
                paths.append(p)
                break
        if paths:
            break

    # If nothing found, fallback to the most recent CSV in out_dir
    if not paths:
        csvs = sorted(outp.rglob("*.csv"))
        if csvs:
            paths.append(csvs[-1])
    if not paths:
        jsons = sorted(outp.rglob("*.json"))
        if jsons:
            paths.append(jsons[-1])

    if not paths:
        return []

    p = paths[0]
    if p.suffix.lower() == ".csv":
        return _read_csv_series(p)
    if p.suffix.lower() == ".json":
        return _read_json_series(p)
    return []

