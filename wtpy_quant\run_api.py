#!/usr/bin/env python3
"""
wtpy_quant API服务启动脚本

使用方法:
    python run_api.py                    # 默认配置启动
    python run_api.py --host 0.0.0.0    # 指定主机
    python run_api.py --port 8080       # 指定端口
    python run_api.py --reload          # 开发模式，自动重载
    python run_api.py --config config.yaml  # 指定配置文件
"""

import argparse
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.main import start_server
from core.config_manager import load_config


def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('wtpy_quant_api.log')
        ]
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="wtpy_quant API服务启动脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
    python run_api.py                           # 默认配置启动
    python run_api.py --host 0.0.0.0 --port 8080  # 指定主机和端口
    python run_api.py --reload --log-level DEBUG   # 开发模式
    python run_api.py --config custom_config.yaml  # 自定义配置
        """
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="服务器主机地址 (默认: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=8000,
        help="服务器端口 (默认: 8000)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (开发模式)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="日志级别 (默认: INFO)"
    )
    
    parser.add_argument(
        "--config",
        type=str,
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数 (默认: 1)"
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        if args.config:
            if not os.path.exists(args.config):
                logger.error(f"配置文件不存在: {args.config}")
                sys.exit(1)
            config = load_config(args.config)
        else:
            config = load_config()
            
        # 从配置文件获取默认值，命令行参数优先
        host = args.host
        port = args.port
        
        if hasattr(config, 'api'):
            if args.host == "127.0.0.1" and hasattr(config.api, 'host'):
                host = config.api.host
            if args.port == 8000 and hasattr(config.api, 'port'):
                port = config.api.port
                
        logger.info(f"启动wtpy_quant API服务...")
        logger.info(f"主机: {host}")
        logger.info(f"端口: {port}")
        logger.info(f"重载模式: {args.reload}")
        logger.info(f"日志级别: {args.log_level}")
        
        if args.config:
            logger.info(f"配置文件: {args.config}")
            
        # 启动服务器
        start_server(
            host=host,
            port=port,
            reload=args.reload,
            log_level=args.log_level.lower()
        )
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"启动服务器失败: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
