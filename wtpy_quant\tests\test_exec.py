"""
交易执行模块测试
"""

import pytest
import asyncio
import tempfile
from unittest.mock import MagicMock, patch, AsyncMock
from pathlib import Path

from wtpy_quant.exec import (
    BrokerAdapter, OrderRouter, RiskEngine, Portfolio,
    Order, Trade, Position, Account,
    OrderSide, OrderType, OrderStatus,
    OrderRequest, RoutingStrategy,
    RiskRule, RiskLevel, RiskAction,
    PortfolioSummary
)


class TestBrokerAdapter:
    """交易适配器测试"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        adapter = BrokerAdapter({"account_id": "test_account"})
        
        # Mock xttrader不可用的情况
        with patch.object(adapter, '_check_xttrader_available', return_value=False):
            success = await adapter.initialize()
            assert not success
            
    @pytest.mark.asyncio
    async def test_place_order(self):
        """测试下单"""
        adapter = BrokerAdapter({"account_id": "test_account"})
        
        # Mock xttrader
        mock_xt_trader = MagicMock()
        mock_xt_trader.order_stock.return_value = 12345  # 成功返回订单ID
        adapter._xt_trader = mock_xt_trader
        adapter._connected = True
        
        # 下单
        order_id = await adapter.place_order(
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=10.50
        )
        
        assert order_id is not None
        assert order_id in adapter._orders
        
        # 验证订单信息
        order = adapter._orders[order_id]
        assert order.symbol == "000001.SZ"
        assert order.side == OrderSide.BUY
        assert order.quantity == 1000
        assert order.price == 10.50
        assert order.status == OrderStatus.SUBMITTED
        
    @pytest.mark.asyncio
    async def test_cancel_order(self):
        """测试撤单"""
        adapter = BrokerAdapter({"account_id": "test_account"})
        
        # 创建测试订单
        order = Order(
            order_id="test_order",
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=10.50,
            status=OrderStatus.SUBMITTED
        )
        adapter._orders["test_order"] = order
        
        # Mock xttrader
        mock_xt_trader = MagicMock()
        mock_xt_trader.cancel_order_stock.return_value = 0  # 成功
        adapter._xt_trader = mock_xt_trader
        adapter._connected = True
        
        # 撤单
        success = await adapter.cancel_order("test_order")
        assert success
        assert order.status == OrderStatus.CANCELLED


class TestOrderRouter:
    """订单路由器测试"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        router = OrderRouter(RoutingStrategy.ROUND_ROBIN)
        assert router.routing_strategy == RoutingStrategy.ROUND_ROBIN
        
    def test_add_account(self):
        """测试添加账户"""
        router = OrderRouter()
        adapter = MagicMock(spec=BrokerAdapter)
        
        router.add_account("account1", adapter, weight=1.0, max_orders=100)
        
        assert "account1" in router._accounts
        account_info = router._accounts["account1"]
        assert account_info.account_id == "account1"
        assert account_info.weight == 1.0
        assert account_info.max_orders == 100
        
    @pytest.mark.asyncio
    async def test_submit_order(self):
        """测试提交订单"""
        router = OrderRouter()
        
        # 添加测试账户
        adapter = MagicMock(spec=BrokerAdapter)
        adapter.place_order = AsyncMock(return_value="order_123")
        router.add_account("account1", adapter)
        
        # 启动路由器
        await router.start()
        
        # 提交订单请求
        order_request = OrderRequest(
            request_id="req_123",
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=10.50
        )
        
        request_id = await router.submit_order(order_request)
        assert request_id == "req_123"
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        # 验证订单已路由
        assert len(router._routed_orders["req_123"]) > 0
        
        await router.stop()


class TestRiskEngine:
    """风险控制引擎测试"""
    
    @pytest.mark.asyncio
    async def test_initialization(self):
        """测试初始化"""
        engine = RiskEngine()
        
        # 验证默认规则已加载
        assert len(engine._risk_rules) > 0
        
        # 验证特定规则存在
        rule_names = [rule.name for rule in engine._risk_rules]
        assert "max_position_ratio" in rule_names
        assert "max_daily_loss" in rule_names
        
    def test_add_risk_rule(self):
        """测试添加风险规则"""
        engine = RiskEngine()
        
        rule = RiskRule(
            name="test_rule",
            description="测试规则",
            rule_type="order",
            condition="quantity > threshold",
            threshold=10000,
            action=RiskAction.REJECT
        )
        
        initial_count = len(engine._risk_rules)
        engine.add_risk_rule(rule)
        
        assert len(engine._risk_rules) == initial_count + 1
        assert rule in engine._risk_rules
        
    @pytest.mark.asyncio
    async def test_check_order_risk(self):
        """测试订单风险检查"""
        engine = RiskEngine()
        
        # 创建测试订单请求
        order_request = OrderRequest(
            request_id="req_123",
            symbol="000001.SZ",
            side=OrderSide.BUY,
            order_type=OrderType.LIMIT,
            quantity=1000,
            price=10.50
        )
        
        # 正常情况应该允许
        action = await engine.check_order_risk(order_request, "test_account")
        assert action == RiskAction.ALLOW
        
    def test_enable_disable_rule(self):
        """测试启用/禁用规则"""
        engine = RiskEngine()
        
        # 禁用规则
        engine.enable_rule("max_position_ratio", False)
        
        rule = next((r for r in engine._risk_rules if r.name == "max_position_ratio"), None)
        assert rule is not None
        assert not rule.enabled
        
        # 启用规则
        engine.enable_rule("max_position_ratio", True)
        assert rule.enabled


class TestPortfolio:
    """投资组合管理器测试"""
    
    def test_initialization(self):
        """测试初始化"""
        portfolio = Portfolio()
        
        assert len(portfolio._accounts) == 0
        assert len(portfolio._positions) == 0
        assert portfolio._stats['total_accounts'] == 0
        
    def test_update_account(self):
        """测试更新账户"""
        portfolio = Portfolio()
        
        account = Account(
            account_id="test_account",
            total_asset=100000.0,
            available_cash=50000.0,
            frozen_cash=0.0,
            market_value=50000.0,
            total_pnl=5000.0
        )
        
        portfolio.update_account(account)
        
        assert "test_account" in portfolio._accounts
        assert portfolio._accounts["test_account"] == account
        
        # 验证净值历史已更新
        assert len(portfolio._nav_history["test_account"]) == 1
        assert portfolio._nav_history["test_account"][0][1] == 100000.0
        
    def test_update_positions(self):
        """测试更新持仓"""
        portfolio = Portfolio()
        
        positions = [
            Position(
                symbol="000001.SZ",
                quantity=1000,
                available_quantity=1000,
                avg_cost=10.0,
                market_value=10500.0,
                unrealized_pnl=500.0,
                realized_pnl=0.0
            ),
            Position(
                symbol="000002.SZ",
                quantity=2000,
                available_quantity=2000,
                avg_cost=20.0,
                market_value=42000.0,
                unrealized_pnl=2000.0,
                realized_pnl=0.0
            )
        ]
        
        portfolio.update_positions("test_account", positions)
        
        assert len(portfolio._positions["test_account"]) == 2
        assert portfolio._stats['total_positions'] == 2
        
    def test_get_portfolio_summary(self):
        """测试获取投资组合摘要"""
        portfolio = Portfolio()
        
        # 添加账户
        account = Account(
            account_id="test_account",
            total_asset=100000.0,
            available_cash=50000.0,
            frozen_cash=0.0,
            market_value=50000.0,
            total_pnl=5000.0
        )
        portfolio.update_account(account)
        
        # 添加持仓
        positions = [
            Position(
                symbol="000001.SZ",
                quantity=1000,
                available_quantity=1000,
                avg_cost=10.0,
                market_value=10500.0,
                unrealized_pnl=500.0,
                realized_pnl=100.0
            ),
            Position(
                symbol="000002.SZ",
                quantity=-500,  # 空头
                available_quantity=500,
                avg_cost=20.0,
                market_value=-10000.0,
                unrealized_pnl=-200.0,
                realized_pnl=50.0
            )
        ]
        portfolio.update_positions("test_account", positions)
        
        # 获取摘要
        summary = portfolio.get_portfolio_summary("test_account")
        
        assert summary is not None
        assert summary.account_id == "test_account"
        assert summary.total_asset == 100000.0
        assert summary.position_count == 2
        assert summary.long_positions == 1
        assert summary.short_positions == 1
        assert summary.realized_pnl == 150.0  # 100 + 50
        assert summary.unrealized_pnl == 300.0  # 500 + (-200)
        
    def test_update_price(self):
        """测试更新价格"""
        portfolio = Portfolio()
        
        portfolio.update_price("000001.SZ", 10.50)
        
        assert portfolio._prices["000001.SZ"] == 10.50
        assert len(portfolio._price_history["000001.SZ"]) == 1
        
        # 再次更新
        portfolio.update_price("000001.SZ", 10.60)
        
        assert portfolio._prices["000001.SZ"] == 10.60
        assert len(portfolio._price_history["000001.SZ"]) == 2


if __name__ == "__main__":
    pytest.main([__file__])
