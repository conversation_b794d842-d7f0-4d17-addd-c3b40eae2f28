"""
BrokerAdapter interface sketch for miniqmt/xttrader integration.
"""
from __future__ import annotations
from dataclasses import dataclass
from typing import Optional


@dataclass
class Order:
    symbol: str
    side: str  # BUY/SELL
    price: float
    qty: int


class BrokerAdapter:
    def place_order(self, order: Order) -> str:
        # Return order id (placeholder)
        return "ORD-DEMO"

    def cancel(self, order_id: str) -> bool:
        return True

    def query_positions(self) -> list[dict]:
        return []

    def query_account(self) -> dict:
        return {"balance": 0.0, "available": 0.0}

