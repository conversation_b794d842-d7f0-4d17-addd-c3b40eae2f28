"""
技术指标库 - 常用技术指标计算

提供常用的技术指标计算函数，包括：
- 趋势指标 (MA, EMA, MACD等)
- 震荡指标 (RSI, KDJ, CCI等)
- 成交量指标 (OBV, VWAP等)
- 波动率指标 (ATR, BB等)
"""

import numpy as np
import pandas as pd
from typing import Tuple, Optional, Union
import talib


def sma(data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
    """
    简单移动平均线
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: SMA值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
    return data.rolling(window=period).mean()


def ema(data: Union[pd.Series, np.ndarray], period: int) -> pd.Series:
    """
    指数移动平均线
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: EMA值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
    return data.ewm(span=period).mean()


def macd(data: Union[pd.Series, np.ndarray], 
         fast_period: int = 12, 
         slow_period: int = 26, 
         signal_period: int = 9) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    MACD指标
    
    Args:
        data: 价格数据
        fast_period: 快线周期
        slow_period: 慢线周期
        signal_period: 信号线周期
        
    Returns:
        Tuple[pd.Series, pd.Series, pd.Series]: (MACD线, 信号线, 柱状图)
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    ema_fast = ema(data, fast_period)
    ema_slow = ema(data, slow_period)
    
    macd_line = ema_fast - ema_slow
    signal_line = ema(macd_line, signal_period)
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


def rsi(data: Union[pd.Series, np.ndarray], period: int = 14) -> pd.Series:
    """
    相对强弱指标
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: RSI值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    delta = data.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    rs = avg_gain / avg_loss
    rsi_values = 100 - (100 / (1 + rs))
    
    return rsi_values


def kdj(high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 9, 
        k_period: int = 3, 
        d_period: int = 3) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    KDJ指标
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        period: RSV周期
        k_period: K值平滑周期
        d_period: D值平滑周期
        
    Returns:
        Tuple[pd.Series, pd.Series, pd.Series]: (K值, D值, J值)
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    # 计算RSV
    lowest_low = low.rolling(window=period).min()
    highest_high = high.rolling(window=period).max()
    rsv = (close - lowest_low) / (highest_high - lowest_low) * 100
    
    # 计算K值
    k = rsv.ewm(span=k_period).mean()
    
    # 计算D值
    d = k.ewm(span=d_period).mean()
    
    # 计算J值
    j = 3 * k - 2 * d
    
    return k, d, j


def bollinger_bands(data: Union[pd.Series, np.ndarray], 
                   period: int = 20, 
                   std_dev: float = 2.0) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    布林带指标
    
    Args:
        data: 价格数据
        period: 周期
        std_dev: 标准差倍数
        
    Returns:
        Tuple[pd.Series, pd.Series, pd.Series]: (上轨, 中轨, 下轨)
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    middle = sma(data, period)
    std = data.rolling(window=period).std()
    
    upper = middle + (std * std_dev)
    lower = middle - (std * std_dev)
    
    return upper, middle, lower


def atr(high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 14) -> pd.Series:
    """
    平均真实波幅
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        period: 周期
        
    Returns:
        pd.Series: ATR值
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    prev_close = close.shift(1)
    
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    
    true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr_values = true_range.rolling(window=period).mean()
    
    return atr_values


def obv(close: Union[pd.Series, np.ndarray], 
        volume: Union[pd.Series, np.ndarray]) -> pd.Series:
    """
    能量潮指标
    
    Args:
        close: 收盘价
        volume: 成交量
        
    Returns:
        pd.Series: OBV值
    """
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
    if isinstance(volume, np.ndarray):
        volume = pd.Series(volume)
        
    price_change = close.diff()
    obv_values = pd.Series(index=close.index, dtype=float)
    obv_values.iloc[0] = volume.iloc[0]
    
    for i in range(1, len(close)):
        if price_change.iloc[i] > 0:
            obv_values.iloc[i] = obv_values.iloc[i-1] + volume.iloc[i]
        elif price_change.iloc[i] < 0:
            obv_values.iloc[i] = obv_values.iloc[i-1] - volume.iloc[i]
        else:
            obv_values.iloc[i] = obv_values.iloc[i-1]
            
    return obv_values


def vwap(high: Union[pd.Series, np.ndarray], 
         low: Union[pd.Series, np.ndarray], 
         close: Union[pd.Series, np.ndarray], 
         volume: Union[pd.Series, np.ndarray]) -> pd.Series:
    """
    成交量加权平均价格
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        volume: 成交量
        
    Returns:
        pd.Series: VWAP值
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
    if isinstance(volume, np.ndarray):
        volume = pd.Series(volume)
        
    typical_price = (high + low + close) / 3
    vwap_values = (typical_price * volume).cumsum() / volume.cumsum()
    
    return vwap_values


def cci(high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 20) -> pd.Series:
    """
    顺势指标
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        period: 周期
        
    Returns:
        pd.Series: CCI值
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    typical_price = (high + low + close) / 3
    sma_tp = typical_price.rolling(window=period).mean()
    mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
    
    cci_values = (typical_price - sma_tp) / (0.015 * mad)
    
    return cci_values


def williams_r(high: Union[pd.Series, np.ndarray], 
               low: Union[pd.Series, np.ndarray], 
               close: Union[pd.Series, np.ndarray], 
               period: int = 14) -> pd.Series:
    """
    威廉指标
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        period: 周期
        
    Returns:
        pd.Series: Williams %R值
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    highest_high = high.rolling(window=period).max()
    lowest_low = low.rolling(window=period).min()
    
    wr = -100 * (highest_high - close) / (highest_high - lowest_low)
    
    return wr


def stochastic(high: Union[pd.Series, np.ndarray], 
               low: Union[pd.Series, np.ndarray], 
               close: Union[pd.Series, np.ndarray], 
               k_period: int = 14, 
               d_period: int = 3) -> Tuple[pd.Series, pd.Series]:
    """
    随机指标
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        k_period: K周期
        d_period: D周期
        
    Returns:
        Tuple[pd.Series, pd.Series]: (K值, D值)
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()
    
    k_percent = 100 * (close - lowest_low) / (highest_high - lowest_low)
    d_percent = k_percent.rolling(window=d_period).mean()
    
    return k_percent, d_percent


def momentum(data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
    """
    动量指标
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: 动量值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    return data - data.shift(period)


def roc(data: Union[pd.Series, np.ndarray], period: int = 10) -> pd.Series:
    """
    变动率指标
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: ROC值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    return (data / data.shift(period) - 1) * 100


def trix(data: Union[pd.Series, np.ndarray], period: int = 14) -> pd.Series:
    """
    三重指数平滑移动平均
    
    Args:
        data: 价格数据
        period: 周期
        
    Returns:
        pd.Series: TRIX值
    """
    if isinstance(data, np.ndarray):
        data = pd.Series(data)
        
    ema1 = ema(data, period)
    ema2 = ema(ema1, period)
    ema3 = ema(ema2, period)
    
    trix_values = (ema3 / ema3.shift(1) - 1) * 10000
    
    return trix_values


def dmi(high: Union[pd.Series, np.ndarray], 
        low: Union[pd.Series, np.ndarray], 
        close: Union[pd.Series, np.ndarray], 
        period: int = 14) -> Tuple[pd.Series, pd.Series, pd.Series]:
    """
    动向指标
    
    Args:
        high: 最高价
        low: 最低价
        close: 收盘价
        period: 周期
        
    Returns:
        Tuple[pd.Series, pd.Series, pd.Series]: (PDI, MDI, ADX)
    """
    if isinstance(high, np.ndarray):
        high = pd.Series(high)
    if isinstance(low, np.ndarray):
        low = pd.Series(low)
    if isinstance(close, np.ndarray):
        close = pd.Series(close)
        
    # 计算TR, +DM, -DM
    prev_high = high.shift(1)
    prev_low = low.shift(1)
    prev_close = close.shift(1)
    
    tr1 = high - low
    tr2 = abs(high - prev_close)
    tr3 = abs(low - prev_close)
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    plus_dm = np.where((high - prev_high) > (prev_low - low), 
                       np.maximum(high - prev_high, 0), 0)
    minus_dm = np.where((prev_low - low) > (high - prev_high), 
                        np.maximum(prev_low - low, 0), 0)
    
    plus_dm = pd.Series(plus_dm, index=high.index)
    minus_dm = pd.Series(minus_dm, index=high.index)
    
    # 计算平滑值
    tr_smooth = tr.rolling(window=period).sum()
    plus_dm_smooth = plus_dm.rolling(window=period).sum()
    minus_dm_smooth = minus_dm.rolling(window=period).sum()
    
    # 计算DI
    pdi = 100 * plus_dm_smooth / tr_smooth
    mdi = 100 * minus_dm_smooth / tr_smooth
    
    # 计算ADX
    dx = 100 * abs(pdi - mdi) / (pdi + mdi)
    adx = dx.rolling(window=period).mean()
    
    return pdi, mdi, adx


# 使用talib的函数（如果可用）
def use_talib_if_available():
    """检查并使用talib库"""
    try:
        import talib
        return True
    except ImportError:
        return False


# 如果talib可用，提供talib版本的函数
if use_talib_if_available():
    def talib_sma(data: np.ndarray, period: int) -> np.ndarray:
        """使用talib计算SMA"""
        return talib.SMA(data, timeperiod=period)
        
    def talib_ema(data: np.ndarray, period: int) -> np.ndarray:
        """使用talib计算EMA"""
        return talib.EMA(data, timeperiod=period)
        
    def talib_macd(data: np.ndarray, 
                   fast_period: int = 12, 
                   slow_period: int = 26, 
                   signal_period: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """使用talib计算MACD"""
        return talib.MACD(data, fastperiod=fast_period, 
                         slowperiod=slow_period, signalperiod=signal_period)
        
    def talib_rsi(data: np.ndarray, period: int = 14) -> np.ndarray:
        """使用talib计算RSI"""
        return talib.RSI(data, timeperiod=period)
