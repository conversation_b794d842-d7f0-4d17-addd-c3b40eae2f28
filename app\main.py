"""
FastAPI application entrypoint for the A-share quant system (wtpy/wondertrader oriented).
This file wires API routers and exposes an ASGI app instance.
"""
from typing import Optional
from pathlib import Path

try:
    from fastapi import FastAPI
    from fastapi.staticfiles import StaticFiles
except Exception:  # pragma: no cover - allow repo to exist without FastAPI installed yet
    FastAPI = None  # type: ignore
    StaticFiles = None  # type: ignore

from .api import health, strategies, accounts, data, backtest, live, ws

APP_TITLE = "WTpy/WonderTrader A-Share Quant System"
APP_VERSION = "0.1.0"


def create_app() -> "FastAPI":
    if FastAPI is None:
        raise RuntimeError("FastAPI is not installed. Please 'pip install fastapi uvicorn' to run the API.")
    app = FastAPI(title=APP_TITLE, version=APP_VERSION)
    app.include_router(health.router)
    app.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
    app.include_router(accounts.router, prefix="/accounts", tags=["accounts"])
    app.include_router(data.router, prefix="/data", tags=["data"])
    app.include_router(backtest.router)
    app.include_router(live.router)
    app.include_router(ws.router)

    # Mount static GUI at /ui
    if StaticFiles is not None:
        static_path = Path(__file__).parent / "gui" / "static"
        app.mount("/ui", StaticFiles(directory=str(static_path), html=True), name="ui")
    return app


# ASGI compatible app for `uvicorn app.main:app --reload`
app: Optional["FastAPI"] = None
try:
    app = create_app()
except Exception:
    # Keep import-time errors soft; running will still raise with actionable message.
    app = None

