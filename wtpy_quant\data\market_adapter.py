"""
市场数据适配器 - miniqmt/xtdata集成

负责实时行情数据的接入和转换，包括：
- miniqmt/xtdata连接管理
- Tick和K线数据订阅
- 数据格式转换
- 断线重连处理
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable, Set
from dataclasses import dataclass
from datetime import datetime
import time
import threading

logger = logging.getLogger(__name__)


@dataclass
class TickData:
    """Tick数据结构"""
    symbol: str
    datetime: str
    price: float
    volume: int
    turnover: float
    bid_price: float
    ask_price: float
    bid_volume: int
    ask_volume: int
    open_interest: int = 0
    
    @classmethod
    def from_xtdata(cls, xt_tick: Any) -> 'TickData':
        """从xtdata格式转换"""
        return cls(
            symbol=getattr(xt_tick, 'stock_code', ''),
            datetime=getattr(xt_tick, 'time', ''),
            price=getattr(xt_tick, 'last_price', 0.0),
            volume=getattr(xt_tick, 'volume', 0),
            turnover=getattr(xt_tick, 'amount', 0.0),
            bid_price=getattr(xt_tick, 'bid_price', [0.0])[0] if getattr(xt_tick, 'bid_price', None) else 0.0,
            ask_price=getattr(xt_tick, 'ask_price', [0.0])[0] if getattr(xt_tick, 'ask_price', None) else 0.0,
            bid_volume=getattr(xt_tick, 'bid_volume', [0])[0] if getattr(xt_tick, 'bid_volume', None) else 0,
            ask_volume=getattr(xt_tick, 'ask_volume', [0])[0] if getattr(xt_tick, 'ask_volume', None) else 0,
            open_interest=getattr(xt_tick, 'open_interest', 0)
        )


@dataclass
class BarData:
    """K线数据结构"""
    symbol: str
    datetime: str
    period: str  # 1m, 5m, 15m, 1h, 1d
    open: float
    high: float
    low: float
    close: float
    volume: int
    turnover: float
    
    @classmethod
    def from_xtdata(cls, xt_bar: Any, period: str = "1m") -> 'BarData':
        """从xtdata格式转换"""
        return cls(
            symbol=getattr(xt_bar, 'stock_code', ''),
            datetime=getattr(xt_bar, 'time', ''),
            period=period,
            open=getattr(xt_bar, 'open', 0.0),
            high=getattr(xt_bar, 'high', 0.0),
            low=getattr(xt_bar, 'low', 0.0),
            close=getattr(xt_bar, 'close', 0.0),
            volume=getattr(xt_bar, 'volume', 0),
            turnover=getattr(xt_bar, 'amount', 0.0)
        )


class MarketDataAdapter:
    """市场数据适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化市场数据适配器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self._xt_data = None
        self._connected = False
        self._subscribed_symbols: Set[str] = set()
        self._tick_callbacks: List[Callable[[TickData], None]] = []
        self._bar_callbacks: List[Callable[[BarData], None]] = []
        self._reconnect_interval = self.config.get('reconnect_interval', 5)
        self._max_reconnect_attempts = self.config.get('max_reconnect_attempts', 10)
        self._reconnect_task: Optional[asyncio.Task] = None
        
        # 统计信息
        self._stats = {
            'ticks_received': 0,
            'bars_received': 0,
            'connection_errors': 0,
            'last_tick_time': 0,
            'last_bar_time': 0
        }
        
    async def initialize(self) -> bool:
        """
        初始化连接
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("Initializing market data adapter...")
            
            # 检查xtdata是否可用
            if not self._check_xtdata_available():
                logger.error("xtdata is not available")
                return False
            
            # 连接到数据源
            if await self._connect():
                logger.info("Market data adapter initialized successfully")
                return True
            else:
                logger.error("Failed to connect to market data source")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize market data adapter: {e}")
            return False
            
    def _check_xtdata_available(self) -> bool:
        """检查xtdata是否可用"""
        try:
            import xtdata
            return True
        except ImportError:
            logger.error("xtdata is not installed. Please install miniqmt/xtquant.")
            return False
            
    async def _connect(self) -> bool:
        """连接到数据源"""
        try:
            import xtdata
            
            # 创建xtdata实例
            self._xt_data = xtdata
            
            # 连接到服务器
            connect_result = self._xt_data.connect()
            if connect_result != 0:
                logger.error(f"Failed to connect to xtdata server: {connect_result}")
                return False
            
            # 注册回调函数
            self._xt_data.subscribe_quote(self._on_tick_data, callback_type='tick')
            self._xt_data.subscribe_quote(self._on_bar_data, callback_type='1m')
            
            self._connected = True
            logger.info("Connected to xtdata server")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to xtdata: {e}")
            self._stats['connection_errors'] += 1
            return False
            
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self._reconnect_task:
                self._reconnect_task.cancel()
                
            if self._xt_data and self._connected:
                # 取消所有订阅
                for symbol in self._subscribed_symbols.copy():
                    await self.unsubscribe_tick(symbol)
                    await self.unsubscribe_bar(symbol)
                
                # 断开连接
                self._xt_data.disconnect()
                self._connected = False
                
            logger.info("Disconnected from market data source")
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
            
    async def subscribe_tick(self, symbols: List[str]) -> bool:
        """
        订阅Tick数据
        
        Args:
            symbols: 股票代码列表
            
        Returns:
            bool: 订阅是否成功
        """
        try:
            if not self._connected:
                logger.error("Not connected to market data source")
                return False
            
            # 订阅Tick数据
            result = self._xt_data.subscribe_quote(symbols, callback=self._on_tick_data)
            if result == 0:
                self._subscribed_symbols.update(symbols)
                logger.info(f"Subscribed to tick data for {len(symbols)} symbols")
                return True
            else:
                logger.error(f"Failed to subscribe tick data: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error subscribing tick data: {e}")
            return False
            
    async def unsubscribe_tick(self, symbol: str) -> bool:
        """
        取消订阅Tick数据
        
        Args:
            symbol: 股票代码
            
        Returns:
            bool: 取消订阅是否成功
        """
        try:
            if not self._connected:
                return True
                
            result = self._xt_data.unsubscribe_quote([symbol])
            if result == 0:
                self._subscribed_symbols.discard(symbol)
                logger.info(f"Unsubscribed tick data for {symbol}")
                return True
            else:
                logger.error(f"Failed to unsubscribe tick data for {symbol}: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error unsubscribing tick data for {symbol}: {e}")
            return False
            
    async def subscribe_bar(self, symbols: List[str], period: str = "1m") -> bool:
        """
        订阅K线数据
        
        Args:
            symbols: 股票代码列表
            period: K线周期
            
        Returns:
            bool: 订阅是否成功
        """
        try:
            if not self._connected:
                logger.error("Not connected to market data source")
                return False
            
            # 订阅K线数据
            callback = lambda data: self._on_bar_data(data, period)
            result = self._xt_data.subscribe_quote(symbols, callback=callback, callback_type=period)
            if result == 0:
                logger.info(f"Subscribed to {period} bar data for {len(symbols)} symbols")
                return True
            else:
                logger.error(f"Failed to subscribe {period} bar data: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error subscribing {period} bar data: {e}")
            return False
            
    async def unsubscribe_bar(self, symbol: str, period: str = "1m") -> bool:
        """
        取消订阅K线数据
        
        Args:
            symbol: 股票代码
            period: K线周期
            
        Returns:
            bool: 取消订阅是否成功
        """
        try:
            if not self._connected:
                return True
                
            result = self._xt_data.unsubscribe_quote([symbol], callback_type=period)
            if result == 0:
                logger.info(f"Unsubscribed {period} bar data for {symbol}")
                return True
            else:
                logger.error(f"Failed to unsubscribe {period} bar data for {symbol}: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error unsubscribing {period} bar data for {symbol}: {e}")
            return False
            
    def register_tick_callback(self, callback: Callable[[TickData], None]) -> None:
        """注册Tick数据回调"""
        self._tick_callbacks.append(callback)
        
    def register_bar_callback(self, callback: Callable[[BarData], None]) -> None:
        """注册K线数据回调"""
        self._bar_callbacks.append(callback)
        
    def _on_tick_data(self, xt_tick: Any) -> None:
        """Tick数据回调处理"""
        try:
            # 转换数据格式
            tick_data = TickData.from_xtdata(xt_tick)
            
            # 更新统计信息
            self._stats['ticks_received'] += 1
            self._stats['last_tick_time'] = time.time()
            
            # 调用注册的回调函数
            for callback in self._tick_callbacks:
                try:
                    callback(tick_data)
                except Exception as e:
                    logger.error(f"Error in tick callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing tick data: {e}")
            
    def _on_bar_data(self, xt_bar: Any, period: str = "1m") -> None:
        """K线数据回调处理"""
        try:
            # 转换数据格式
            bar_data = BarData.from_xtdata(xt_bar, period)
            
            # 更新统计信息
            self._stats['bars_received'] += 1
            self._stats['last_bar_time'] = time.time()
            
            # 调用注册的回调函数
            for callback in self._bar_callbacks:
                try:
                    callback(bar_data)
                except Exception as e:
                    logger.error(f"Error in bar callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing bar data: {e}")
            
    async def _start_reconnect_monitor(self) -> None:
        """启动重连监控"""
        self._reconnect_task = asyncio.create_task(self._reconnect_loop())
        
    async def _reconnect_loop(self) -> None:
        """重连循环"""
        attempt = 0
        while attempt < self._max_reconnect_attempts:
            try:
                await asyncio.sleep(self._reconnect_interval)
                
                if not self._connected:
                    logger.info(f"Attempting to reconnect... (attempt {attempt + 1})")
                    if await self._connect():
                        # 重新订阅之前的标的
                        if self._subscribed_symbols:
                            symbols = list(self._subscribed_symbols)
                            self._subscribed_symbols.clear()
                            await self.subscribe_tick(symbols)
                        break
                    else:
                        attempt += 1
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in reconnect loop: {e}")
                attempt += 1
                
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected
        
    def get_subscribed_symbols(self) -> Set[str]:
        """获取已订阅的标的"""
        return self._subscribed_symbols.copy()
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
