try:
    from fastapi import APIRouter
    from pydantic import BaseModel
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    BaseModel = object  # type: ignore

router = APIRouter() if APIRouter else None


class Account(BaseModel):  # type: ignore[misc]
    account_id: str
    balance: float
    available: float


_FAKE_ACCOUNTS = [
    Account(account_id="demo", balance=1_000_000.0, available=1_000_000.0)  # type: ignore[arg-type]
]


if router:
    @router.get("/", response_model=list[Account])  # type: ignore[arg-type]
    async def list_accounts():
        return _FAKE_ACCOUNTS

