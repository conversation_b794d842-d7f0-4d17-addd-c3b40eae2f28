# wtpy_quant 默认配置文件

# 系统配置
system:
  name: "wtpy_quant"
  version: "1.0.0"
  log_level: "INFO"
  log_file: "logs/wtpy_quant.log"
  
# wtpy引擎配置
engine:
  # 引擎类型：live(实盘) / backtest(回测)
  mode: "live"
  # 数据路径
  data_path: "data/"
  # 策略路径
  strategy_path: "strategies/"
  # 配置文件路径
  config_path: "config/"
  
# 数据配置
data:
  # 实时行情源
  market_source: "miniqmt"
  # mmap缓存配置
  mmap:
    enabled: true
    path: "data/mmap/"
    days_to_keep: 7
    tick_buffer_size: 100000
    bar_buffer_size: 10000
  # 长期存储配置
  longterm:
    type: "parquet"  # parquet / clickhouse / postgresql
    path: "data/parquet/"
    
# 交易配置
trading:
  # 交易接口
  broker: "miniqmt"
  # 风控配置
  risk:
    max_position_ratio: 0.95
    max_single_stock_ratio: 0.1
    max_daily_loss_ratio: 0.05
    max_orders_per_second: 10
    
# API服务配置
api:
  host: "0.0.0.0"
  port: 8000
  reload: false
  workers: 1
  
# 回测配置
backtest:
  # 默认回测参数
  start_date: "20230101"
  end_date: "20231231"
  initial_capital: 1000000
  # 成本配置
  costs:
    commission_rate: 0.0003
    stamp_tax_rate: 0.001
    slippage_rate: 0.0001
