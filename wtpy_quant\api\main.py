"""
FastAPI主应用 - wtpy量化系统API服务

提供完整的REST API服务，包括：
- 策略管理API
- 回测任务API
- 实时监控API
- 数据查询API
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from ..core.config_manager import SystemConfig, load_config
from ..core.engine import WtpyEngine
from ..backtest.backtest_engine import BacktestEngine
from ..data.concept_manager import ConceptManager
from .routers import strategy, backtest, data, monitoring

logger = logging.getLogger(__name__)

# 全局应用状态
app_state = {
    'config': None,
    'engine': None,
    'backtest_engine': None,
    'concept_manager': None,
    'initialized': False
}


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("Starting wtpy_quant API service...")
    
    try:
        # 加载配置
        config = load_config()
        app_state['config'] = config
        
        # 初始化引擎
        engine = WtpyEngine(config.engine)
        app_state['engine'] = engine
        
        # 初始化回测引擎
        backtest_engine = BacktestEngine(config.backtest)
        app_state['backtest_engine'] = backtest_engine
        
        # 初始化概念管理器
        concept_manager = ConceptManager("local")
        await concept_manager.initialize()
        app_state['concept_manager'] = concept_manager
        
        app_state['initialized'] = True
        logger.info("wtpy_quant API service started successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize API service: {e}")
        app_state['initialized'] = False
        
    yield
    
    # 关闭时清理
    logger.info("Shutting down wtpy_quant API service...")
    
    try:
        if app_state['engine']:
            app_state['engine'].stop()
            
        logger.info("wtpy_quant API service stopped")
        
    except Exception as e:
        logger.error(f"Error during shutdown: {e}")


# 创建FastAPI应用
app = FastAPI(
    title="wtpy_quant API",
    description="wtpy量化交易系统API服务",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# 依赖注入函数
def get_config() -> SystemConfig:
    """获取系统配置"""
    if not app_state['initialized'] or not app_state['config']:
        raise HTTPException(status_code=503, detail="Service not initialized")
    return app_state['config']


def get_engine() -> WtpyEngine:
    """获取wtpy引擎"""
    if not app_state['initialized'] or not app_state['engine']:
        raise HTTPException(status_code=503, detail="Engine not initialized")
    return app_state['engine']


def get_backtest_engine() -> BacktestEngine:
    """获取回测引擎"""
    if not app_state['initialized'] or not app_state['backtest_engine']:
        raise HTTPException(status_code=503, detail="Backtest engine not initialized")
    return app_state['backtest_engine']


def get_concept_manager() -> ConceptManager:
    """获取概念管理器"""
    if not app_state['initialized'] or not app_state['concept_manager']:
        raise HTTPException(status_code=503, detail="Concept manager not initialized")
    return app_state['concept_manager']


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "wtpy_quant API Service",
        "version": "1.0.0",
        "status": "running" if app_state['initialized'] else "initializing"
    }


# 健康检查
@app.get("/health")
async def health_check():
    """健康检查"""
    if not app_state['initialized']:
        raise HTTPException(status_code=503, detail="Service not ready")
        
    return {
        "status": "healthy",
        "timestamp": asyncio.get_event_loop().time(),
        "components": {
            "config": app_state['config'] is not None,
            "engine": app_state['engine'] is not None,
            "backtest_engine": app_state['backtest_engine'] is not None,
            "concept_manager": app_state['concept_manager'] is not None
        }
    }


# 系统信息
@app.get("/info")
async def system_info(config: SystemConfig = Depends(get_config)):
    """获取系统信息"""
    return {
        "system": {
            "name": "wtpy_quant",
            "version": "1.0.0",
            "description": "wtpy量化交易系统"
        },
        "config": {
            "engine_mode": config.engine.mode,
            "data_path": config.data.path,
            "api_host": config.api.host,
            "api_port": config.api.port
        },
        "status": {
            "initialized": app_state['initialized'],
            "engine_running": app_state['engine'].is_running() if app_state['engine'] else False
        }
    }


# 注册路由
app.include_router(strategy.router, prefix="/api/v1/strategy", tags=["策略管理"])
app.include_router(backtest.router, prefix="/api/v1/backtest", tags=["回测管理"])
app.include_router(data.router, prefix="/api/v1/data", tags=["数据服务"])
app.include_router(monitoring.router, prefix="/api/v1/monitoring", tags=["监控服务"])


# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "Internal server error",
            "message": str(exc),
            "type": type(exc).__name__
        }
    )


# 启动函数
def start_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    reload: bool = False,
    log_level: str = "info"
):
    """启动API服务器"""
    uvicorn.run(
        "wtpy_quant.api.main:app",
        host=host,
        port=port,
        reload=reload,
        log_level=log_level,
        access_log=True
    )


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动服务器
    start_server(reload=True)
