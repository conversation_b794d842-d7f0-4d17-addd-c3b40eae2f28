from pathlib import Path
try:
    from fastapi import APIRouter
    from pydantic import BaseModel
except Exception:  # pragma: no cover
    APIRouter = None  # type: ignore
    BaseModel = object  # type: ignore

from app.core.wtpy_integration import BacktestFacade

router = APIRouter(prefix="/bt", tags=["backtest"]) if APIRouter else None


class BacktestRequest(BaseModel):  # type: ignore[misc]
    strategy: str  # path to strategy script or name referenced by config
    start: str
    end: str
    config_path: str | None = None
    out_dir: str | None = None


if router:
    @router.post("/run")
    async def run_backtest(req: BacktestRequest):
        cfg = req.config_path or str(Path("app/config/engine.config.json").resolve())
        outd = req.out_dir or str(Path("runs/backtest").resolve())
        fac = BacktestFacade()
        result = fac.run(strategy_py=req.strategy, config_path=cfg, out_dir=outd)
        return {"ok": result.ok, "message": result.message, "details": result.details}

