"""
策略管理器 - 策略生命周期管理

负责策略的加载、启动、停止和监控，包括：
- 策略注册与发现
- 策略生命周期管理
- 策略状态监控
- 策略参数管理
"""

import asyncio
import importlib
import inspect
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from enum import Enum
import time

from .event_bridge import EventBridge, EventType, Event

logger = logging.getLogger(__name__)


class StrategyState(Enum):
    """策略状态枚举"""
    UNKNOWN = "unknown"
    LOADED = "loaded"
    INITIALIZED = "initialized"
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class StrategyInfo:
    """策略信息"""
    name: str
    module_path: str
    class_name: str
    description: str = ""
    version: str = "1.0.0"
    author: str = ""
    params: Dict[str, Any] = field(default_factory=dict)
    state: StrategyState = StrategyState.UNKNOWN
    instance: Optional[Any] = None
    error_message: str = ""
    created_time: float = field(default_factory=time.time)
    updated_time: float = field(default_factory=time.time)


class StrategyManager:
    """策略管理器"""
    
    def __init__(self, event_bridge: EventBridge, strategy_path: Optional[Union[str, Path]] = None):
        """
        初始化策略管理器
        
        Args:
            event_bridge: 事件桥接器
            strategy_path: 策略文件路径
        """
        self.event_bridge = event_bridge
        self.strategy_path = Path(strategy_path) if strategy_path else Path("strategies")
        self._strategies: Dict[str, StrategyInfo] = {}
        self._running_strategies: Dict[str, Any] = {}
        
    def register_strategy(self, 
                         name: str,
                         module_path: str,
                         class_name: str,
                         description: str = "",
                         version: str = "1.0.0",
                         author: str = "",
                         params: Optional[Dict[str, Any]] = None) -> bool:
        """
        注册策略
        
        Args:
            name: 策略名称
            module_path: 模块路径
            class_name: 类名
            description: 描述
            version: 版本
            author: 作者
            params: 参数
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if name in self._strategies:
                logger.warning(f"Strategy {name} already registered, updating...")
                
            strategy_info = StrategyInfo(
                name=name,
                module_path=module_path,
                class_name=class_name,
                description=description,
                version=version,
                author=author,
                params=params or {},
                state=StrategyState.LOADED
            )
            
            self._strategies[name] = strategy_info
            logger.info(f"Strategy {name} registered successfully")
            
            # 发送策略注册事件
            asyncio.create_task(self.event_bridge.emit(Event(
                type=EventType.STRATEGY_INIT,
                data=strategy_info,
                source="strategy_manager"
            )))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to register strategy {name}: {e}")
            return False
            
    def unregister_strategy(self, name: str) -> bool:
        """
        注销策略
        
        Args:
            name: 策略名称
            
        Returns:
            bool: 注销是否成功
        """
        try:
            if name not in self._strategies:
                logger.warning(f"Strategy {name} not found")
                return False
                
            # 如果策略正在运行，先停止
            if name in self._running_strategies:
                self.stop_strategy(name)
                
            del self._strategies[name]
            logger.info(f"Strategy {name} unregistered successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister strategy {name}: {e}")
            return False
            
    def load_strategy(self, name: str) -> bool:
        """
        加载策略实例
        
        Args:
            name: 策略名称
            
        Returns:
            bool: 加载是否成功
        """
        try:
            if name not in self._strategies:
                logger.error(f"Strategy {name} not registered")
                return False
                
            strategy_info = self._strategies[name]
            
            # 动态导入模块
            module = importlib.import_module(strategy_info.module_path)
            strategy_class = getattr(module, strategy_info.class_name)
            
            # 创建策略实例
            strategy_instance = strategy_class(**strategy_info.params)
            
            # 更新策略信息
            strategy_info.instance = strategy_instance
            strategy_info.state = StrategyState.INITIALIZED
            strategy_info.updated_time = time.time()
            
            logger.info(f"Strategy {name} loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load strategy {name}: {e}")
            strategy_info = self._strategies.get(name)
            if strategy_info:
                strategy_info.state = StrategyState.ERROR
                strategy_info.error_message = str(e)
                strategy_info.updated_time = time.time()
            return False
            
    def start_strategy(self, name: str, ctx: Optional[Any] = None) -> bool:
        """
        启动策略
        
        Args:
            name: 策略名称
            ctx: wtpy上下文对象
            
        Returns:
            bool: 启动是否成功
        """
        try:
            if name not in self._strategies:
                logger.error(f"Strategy {name} not registered")
                return False
                
            strategy_info = self._strategies[name]
            
            # 如果策略未加载，先加载
            if strategy_info.instance is None:
                if not self.load_strategy(name):
                    return False
                    
            strategy_instance = strategy_info.instance
            
            # 调用策略初始化方法
            if hasattr(strategy_instance, 'on_init'):
                strategy_instance.on_init(ctx)
                
            # 添加到运行列表
            self._running_strategies[name] = strategy_instance
            
            # 更新状态
            strategy_info.state = StrategyState.RUNNING
            strategy_info.updated_time = time.time()
            
            logger.info(f"Strategy {name} started successfully")
            
            # 发送策略启动事件
            asyncio.create_task(self.event_bridge.emit(Event(
                type=EventType.STRATEGY_START,
                data=strategy_info,
                source="strategy_manager"
            )))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to start strategy {name}: {e}")
            strategy_info = self._strategies.get(name)
            if strategy_info:
                strategy_info.state = StrategyState.ERROR
                strategy_info.error_message = str(e)
                strategy_info.updated_time = time.time()
                
                # 发送策略错误事件
                asyncio.create_task(self.event_bridge.emit(Event(
                    type=EventType.STRATEGY_ERROR,
                    data={"name": name, "error": str(e)},
                    source="strategy_manager"
                )))
            return False
            
    def stop_strategy(self, name: str) -> bool:
        """
        停止策略
        
        Args:
            name: 策略名称
            
        Returns:
            bool: 停止是否成功
        """
        try:
            if name not in self._strategies:
                logger.error(f"Strategy {name} not registered")
                return False
                
            if name not in self._running_strategies:
                logger.warning(f"Strategy {name} is not running")
                return True
                
            strategy_info = self._strategies[name]
            strategy_instance = self._running_strategies[name]
            
            # 调用策略停止方法（如果存在）
            if hasattr(strategy_instance, 'on_stop'):
                strategy_instance.on_stop()
                
            # 从运行列表中移除
            del self._running_strategies[name]
            
            # 更新状态
            strategy_info.state = StrategyState.STOPPED
            strategy_info.updated_time = time.time()
            
            logger.info(f"Strategy {name} stopped successfully")
            
            # 发送策略停止事件
            asyncio.create_task(self.event_bridge.emit(Event(
                type=EventType.STRATEGY_STOP,
                data=strategy_info,
                source="strategy_manager"
            )))
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop strategy {name}: {e}")
            return False
            
    def get_strategy_info(self, name: str) -> Optional[StrategyInfo]:
        """
        获取策略信息
        
        Args:
            name: 策略名称
            
        Returns:
            Optional[StrategyInfo]: 策略信息
        """
        return self._strategies.get(name)
        
    def list_strategies(self) -> List[StrategyInfo]:
        """
        获取所有策略列表
        
        Returns:
            List[StrategyInfo]: 策略信息列表
        """
        return list(self._strategies.values())
        
    def get_running_strategies(self) -> List[str]:
        """
        获取正在运行的策略列表
        
        Returns:
            List[str]: 运行中的策略名称列表
        """
        return list(self._running_strategies.keys())
        
    def update_strategy_params(self, name: str, params: Dict[str, Any]) -> bool:
        """
        更新策略参数
        
        Args:
            name: 策略名称
            params: 新参数
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if name not in self._strategies:
                logger.error(f"Strategy {name} not registered")
                return False
                
            strategy_info = self._strategies[name]
            strategy_info.params.update(params)
            strategy_info.updated_time = time.time()
            
            # 如果策略正在运行，需要重新加载
            if name in self._running_strategies:
                logger.info(f"Strategy {name} is running, reloading with new params...")
                self.stop_strategy(name)
                strategy_info.instance = None  # 清除实例，强制重新加载
                self.start_strategy(name)
                
            logger.info(f"Strategy {name} params updated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update strategy {name} params: {e}")
            return False
            
    def discover_strategies(self) -> int:
        """
        自动发现策略文件
        
        Returns:
            int: 发现的策略数量
        """
        discovered_count = 0
        
        try:
            if not self.strategy_path.exists():
                logger.warning(f"Strategy path {self.strategy_path} does not exist")
                return 0
                
            # 遍历策略目录
            for py_file in self.strategy_path.glob("**/*.py"):
                if py_file.name.startswith("__"):
                    continue
                    
                try:
                    # 构建模块路径
                    relative_path = py_file.relative_to(self.strategy_path.parent)
                    module_path = str(relative_path.with_suffix("")).replace("/", ".").replace("\\", ".")
                    
                    # 动态导入模块
                    module = importlib.import_module(module_path)
                    
                    # 查找策略类
                    for name, obj in inspect.getmembers(module, inspect.isclass):
                        if (hasattr(obj, 'on_init') or hasattr(obj, 'on_tick') or hasattr(obj, 'on_bar')):
                            strategy_name = f"{py_file.stem}_{name}"
                            
                            # 获取策略元信息
                            description = getattr(obj, '__doc__', '') or ""
                            version = getattr(obj, '__version__', '1.0.0')
                            author = getattr(obj, '__author__', '')
                            
                            self.register_strategy(
                                name=strategy_name,
                                module_path=module_path,
                                class_name=name,
                                description=description.strip(),
                                version=version,
                                author=author
                            )
                            discovered_count += 1
                            
                except Exception as e:
                    logger.warning(f"Failed to discover strategies in {py_file}: {e}")
                    
            logger.info(f"Discovered {discovered_count} strategies")
            return discovered_count
            
        except Exception as e:
            logger.error(f"Failed to discover strategies: {e}")
            return 0
            
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'total_strategies': len(self._strategies),
            'running_strategies': len(self._running_strategies),
            'strategies_by_state': {
                state.value: len([s for s in self._strategies.values() if s.state == state])
                for state in StrategyState
            }
        }
