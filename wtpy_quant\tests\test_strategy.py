"""
策略模块测试
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import MagicMock, patch

from wtpy_quant.strategy import (
    BaseStrategy, ConceptStrategy, MicrostructureStrategy,
    indicators
)


class TestStrategy(BaseStrategy):
    """测试策略类"""
    
    def on_init(self, context):
        self.log_info("Test strategy initialized")
        
    def on_tick(self, context, stdCode, newTick):
        pass
        
    def on_bar(self, context, stdCode, period, newBar):
        pass


class TestBaseStrategy:
    """策略基类测试"""
    
    def test_initialization(self):
        """测试初始化"""
        strategy = TestStrategy("TestStrategy", param1=10, param2="test")
        
        assert strategy.name == "TestStrategy"
        assert strategy.get_param("param1") == 10
        assert strategy.get_param("param2") == "test"
        assert strategy.get_param("nonexistent", "default") == "default"
        
    def test_parameter_management(self):
        """测试参数管理"""
        strategy = TestStrategy("TestStrategy")
        
        # 设置参数
        strategy.set_param("new_param", 100)
        assert strategy.get_param("new_param") == 100
        
        # 获取所有参数
        params = strategy.params
        assert "new_param" in params
        assert params["new_param"] == 100
        
    def test_position_tracking(self):
        """测试持仓跟踪"""
        strategy = TestStrategy("TestStrategy")
        
        # 模拟成交回调
        mock_context = MagicMock()
        
        # 买入成交
        strategy.on_trade(mock_context, 1, "000001.SZ", True, 1000, 10.0)
        assert strategy._positions["000001.SZ"] == 1000
        
        # 卖出成交
        strategy.on_trade(mock_context, 2, "000001.SZ", False, 500, 10.5)
        assert strategy._positions["000001.SZ"] == 500
        
        # 获取持仓
        positions = strategy.get_positions()
        assert positions["000001.SZ"] == 500
        
    def test_statistics(self):
        """测试统计信息"""
        strategy = TestStrategy("TestStrategy")
        
        # 初始统计
        stats = strategy.get_stats()
        assert stats['total_signals'] == 0
        assert stats['orders_placed'] == 0
        
        # 模拟交易
        mock_context = MagicMock()
        mock_context.stra_enter_long = MagicMock()
        
        strategy.buy(mock_context, "000001.SZ", 1000, 10.0)
        
        stats = strategy.get_stats()
        assert stats['buy_signals'] == 1
        assert stats['orders_placed'] == 1
        assert stats['total_signals'] == 1
        
    def test_trading_time_check(self):
        """测试交易时间判断"""
        strategy = TestStrategy("TestStrategy")
        
        # 这个测试依赖于当前时间，实际使用中可能需要mock
        is_trading = strategy.is_trading_time()
        assert isinstance(is_trading, bool)
        
    def test_signal_callbacks(self):
        """测试信号回调"""
        strategy = TestStrategy("TestStrategy")
        
        # 注册回调
        callback_called = []
        def test_callback(signal_type, symbol, qty, price):
            callback_called.append((signal_type, symbol, qty, price))
            
        strategy.register_signal_callback(test_callback)
        
        # 触发信号
        mock_context = MagicMock()
        mock_context.stra_enter_long = MagicMock()
        
        strategy.buy(mock_context, "000001.SZ", 1000, 10.0)
        
        assert len(callback_called) == 1
        assert callback_called[0] == ("buy", "000001.SZ", 1000, 10.0)


class TestIndicators:
    """技术指标测试"""
    
    def test_sma(self):
        """测试简单移动平均"""
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        sma_5 = indicators.sma(data, 5)
        
        # 检查最后几个值
        assert abs(sma_5.iloc[-1] - 8.0) < 0.001  # (6+7+8+9+10)/5 = 8
        assert abs(sma_5.iloc[-2] - 7.0) < 0.001  # (5+6+7+8+9)/5 = 7
        
    def test_ema(self):
        """测试指数移动平均"""
        data = pd.Series([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
        ema_5 = indicators.ema(data, 5)
        
        # EMA应该比SMA更接近最新价格
        assert not ema_5.isna().all()
        assert ema_5.iloc[-1] > 8.0  # 应该大于SMA值
        
    def test_rsi(self):
        """测试RSI指标"""
        # 创建有趋势的数据
        data = pd.Series([10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 
                         19, 18, 17, 16, 15, 14, 13, 12, 11, 10])
        rsi_values = indicators.rsi(data, 14)
        
        # RSI应该在0-100之间
        valid_rsi = rsi_values.dropna()
        assert all(0 <= val <= 100 for val in valid_rsi)
        
    def test_macd(self):
        """测试MACD指标"""
        data = pd.Series(np.random.randn(100).cumsum() + 100)
        macd_line, signal_line, histogram = indicators.macd(data)
        
        assert len(macd_line) == len(data)
        assert len(signal_line) == len(data)
        assert len(histogram) == len(data)
        
        # 柱状图应该等于MACD线减去信号线
        diff = (macd_line - signal_line - histogram).dropna()
        assert all(abs(val) < 1e-10 for val in diff)
        
    def test_bollinger_bands(self):
        """测试布林带"""
        data = pd.Series(np.random.randn(50).cumsum() + 100)
        upper, middle, lower = indicators.bollinger_bands(data, 20, 2.0)
        
        # 上轨应该大于中轨，中轨应该大于下轨
        valid_data = pd.DataFrame({'upper': upper, 'middle': middle, 'lower': lower}).dropna()
        
        for _, row in valid_data.iterrows():
            assert row['upper'] >= row['middle']
            assert row['middle'] >= row['lower']
            
    def test_kdj(self):
        """测试KDJ指标"""
        high = pd.Series([15, 16, 17, 18, 19, 20, 19, 18, 17, 16, 15])
        low = pd.Series([10, 11, 12, 13, 14, 15, 14, 13, 12, 11, 10])
        close = pd.Series([12, 13, 14, 15, 16, 17, 16, 15, 14, 13, 12])
        
        k, d, j = indicators.kdj(high, low, close)
        
        # K、D值应该在0-100之间，J值可能超出范围
        valid_k = k.dropna()
        valid_d = d.dropna()
        
        assert all(0 <= val <= 100 for val in valid_k)
        assert all(0 <= val <= 100 for val in valid_d)


class TestConceptStrategy:
    """概念策略测试"""
    
    def test_initialization(self):
        """测试初始化"""
        strategy = ConceptStrategy(
            max_positions=5,
            position_size=0.2,
            stop_loss=-0.03
        )
        
        assert strategy.name == "ConceptStrategy"
        assert strategy.max_positions == 5
        assert strategy.position_size == 0.2
        assert strategy.stop_loss == -0.03
        
    def test_parameter_access(self):
        """测试参数访问"""
        strategy = ConceptStrategy()
        
        # 默认参数
        assert strategy.max_positions == 10
        assert strategy.concept_lookback == 5
        assert strategy.min_concept_return == 0.02
        
    @patch('wtpy_quant.strategy.concept_strategy.ConceptManager')
    def test_hot_concepts_update(self, mock_concept_manager):
        """测试热门概念更新"""
        strategy = ConceptStrategy()
        
        # Mock概念管理器
        mock_manager = MagicMock()
        mock_performance = MagicMock()
        mock_performance.concept_code = "AI"
        mock_performance.change_pct = 0.05
        
        mock_manager.get_top_concepts.return_value = [mock_performance]
        strategy.concept_manager = mock_manager
        
        # 更新热门概念
        strategy._update_hot_concepts()
        
        assert "AI" in strategy.hot_concepts
        
    def test_stock_score_calculation(self):
        """测试股票评分计算"""
        strategy = ConceptStrategy()
        
        # Mock上下文和数据
        mock_context = MagicMock()
        
        # 创建模拟K线数据
        dates = pd.date_range('2023-01-01', periods=100, freq='D')
        mock_bars = pd.DataFrame({
            'open': np.random.randn(100).cumsum() + 100,
            'high': np.random.randn(100).cumsum() + 102,
            'low': np.random.randn(100).cumsum() + 98,
            'close': np.random.randn(100).cumsum() + 100,
            'volume': np.random.randint(1000, 10000, 100)
        }, index=dates)
        
        mock_context.stra_get_bars.return_value = mock_bars.to_dict('records')
        
        # 计算评分
        score = strategy._calculate_stock_score(mock_context, "000001.SZ")
        
        assert isinstance(score, float)
        assert score >= 0.0


class TestMicrostructureStrategy:
    """微结构策略测试"""
    
    def test_initialization(self):
        """测试初始化"""
        strategy = MicrostructureStrategy(
            target_symbols=["000001.SZ", "000002.SZ"],
            tick_window=50,
            signal_threshold=0.7
        )
        
        assert strategy.name == "MicrostructureStrategy"
        assert len(strategy.target_symbols) == 2
        assert strategy.tick_window == 50
        assert strategy.signal_threshold == 0.7
        
    def test_tick_data_parsing(self):
        """测试Tick数据解析"""
        strategy = MicrostructureStrategy()
        
        # Mock tick数据
        mock_tick = MagicMock()
        mock_tick.actiontime = 1640995200  # 2022-01-01 00:00:00
        mock_tick.price = 10.50
        mock_tick.volume = 1000
        mock_tick.bidprice = [10.49]
        mock_tick.askprice = [10.51]
        mock_tick.bidqty = [500]
        mock_tick.askqty = [800]
        mock_tick.total_volume = 50000
        mock_tick.turnover = 525000.0
        
        tick_data = strategy._parse_tick_data(mock_tick)
        
        assert tick_data['time'] == 1640995200
        assert tick_data['price'] == 10.50
        assert tick_data['volume'] == 1000
        assert tick_data['bid_price'] == 10.49
        assert tick_data['ask_price'] == 10.51
        
    def test_spread_factor_calculation(self):
        """测试价差因子计算"""
        strategy = MicrostructureStrategy()
        
        # 创建模拟tick数据
        ticks = []
        for i in range(20):
            tick = {
                'time': 1640995200 + i,
                'price': 10.0 + i * 0.01,
                'bid_price': 10.0 + i * 0.01 - 0.01,
                'ask_price': 10.0 + i * 0.01 + 0.01,
                'bid_volume': 1000,
                'ask_volume': 1000
            }
            ticks.append(tick)
            
        spread_factor = strategy._calculate_spread_factor(ticks)
        
        assert isinstance(spread_factor, float)
        assert -1.0 <= spread_factor <= 1.0
        
    def test_imbalance_factor_calculation(self):
        """测试不平衡因子计算"""
        strategy = MicrostructureStrategy()
        
        # 创建买单占优的数据
        ticks = []
        for i in range(20):
            tick = {
                'bid_volume': 2000,  # 买单更多
                'ask_volume': 1000
            }
            ticks.append(tick)
            
        imbalance_factor = strategy._calculate_imbalance_factor(ticks)
        
        assert isinstance(imbalance_factor, float)
        assert imbalance_factor > 0  # 买单占优应该为正
        assert -1.0 <= imbalance_factor <= 1.0
        
    def test_signal_generation(self):
        """测试信号生成"""
        strategy = MicrostructureStrategy(signal_threshold=0.5)
        
        # 强买入信号
        factors = {
            'spread': 0.8,
            'depth': 0.6,
            'imbalance': 0.7,
            'momentum': 0.5,
            'volume': 0.3,
            'volatility': 0.2
        }
        
        signal = strategy._generate_signal("000001.SZ", factors)
        assert signal == 1  # 买入信号
        
        # 强卖出信号
        factors = {
            'spread': -0.8,
            'depth': -0.6,
            'imbalance': -0.7,
            'momentum': -0.5,
            'volume': 0.3,
            'volatility': 0.2
        }
        
        signal = strategy._generate_signal("000001.SZ", factors)
        assert signal == -1  # 卖出信号
        
        # 无信号
        factors = {
            'spread': 0.1,
            'depth': 0.1,
            'imbalance': 0.1,
            'momentum': 0.1,
            'volume': 0.1,
            'volatility': 0.1
        }
        
        signal = strategy._generate_signal("000001.SZ", factors)
        assert signal == 0  # 无信号


if __name__ == "__main__":
    pytest.main([__file__])
