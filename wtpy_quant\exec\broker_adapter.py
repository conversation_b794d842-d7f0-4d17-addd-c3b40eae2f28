"""
交易适配器 - miniqmt交易接口集成

负责与miniqmt交易接口的集成，包括：
- 订单下单与撤单
- 账户与持仓查询
- 成交回报处理
- 连接管理与重连
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
import time
import uuid

logger = logging.getLogger(__name__)


class OrderSide(Enum):
    """订单方向"""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"      # 市价单
    LIMIT = "limit"        # 限价单
    STOP = "stop"          # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"        # 待报
    SUBMITTED = "submitted"    # 已报
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"          # 全部成交
    CANCELLED = "cancelled"    # 已撤销
    REJECTED = "rejected"      # 已拒绝
    ERROR = "error"           # 错误


@dataclass
class Order:
    """订单信息"""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: int
    price: float = 0.0
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: int = 0
    avg_price: float = 0.0
    commission: float = 0.0
    create_time: str = ""
    update_time: str = ""
    error_message: str = ""
    
    def __post_init__(self):
        if not self.create_time:
            self.create_time = time.strftime("%Y%m%d %H:%M:%S")
        if not self.update_time:
            self.update_time = self.create_time


@dataclass
class Trade:
    """成交信息"""
    trade_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: int
    price: float
    commission: float
    trade_time: str = ""
    
    def __post_init__(self):
        if not self.trade_time:
            self.trade_time = time.strftime("%Y%m%d %H:%M:%S")


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    available_quantity: int
    avg_cost: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    update_time: str = ""
    
    def __post_init__(self):
        if not self.update_time:
            self.update_time = time.strftime("%Y%m%d %H:%M:%S")


@dataclass
class Account:
    """账户信息"""
    account_id: str
    total_asset: float
    available_cash: float
    frozen_cash: float
    market_value: float
    total_pnl: float
    update_time: str = ""
    
    def __post_init__(self):
        if not self.update_time:
            self.update_time = time.strftime("%Y%m%d %H:%M:%S")


class BrokerAdapter:
    """交易适配器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化交易适配器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self._xt_trader = None
        self._connected = False
        self._account_id = self.config.get("account_id", "")
        
        # 回调函数
        self._order_callbacks: List[Callable[[Order], None]] = []
        self._trade_callbacks: List[Callable[[Trade], None]] = []
        self._position_callbacks: List[Callable[[Position], None]] = []
        self._account_callbacks: List[Callable[[Account], None]] = []
        
        # 订单缓存
        self._orders: Dict[str, Order] = {}
        self._trades: Dict[str, Trade] = {}
        
        # 统计信息
        self._stats = {
            'orders_submitted': 0,
            'orders_filled': 0,
            'orders_cancelled': 0,
            'orders_rejected': 0,
            'total_trades': 0,
            'connection_errors': 0
        }
        
    async def initialize(self) -> bool:
        """
        初始化连接
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("Initializing broker adapter...")
            
            # 检查xttrader是否可用
            if not self._check_xttrader_available():
                logger.error("xttrader is not available")
                return False
            
            # 连接到交易服务器
            if await self._connect():
                logger.info("Broker adapter initialized successfully")
                return True
            else:
                logger.error("Failed to connect to trading server")
                return False
                
        except Exception as e:
            logger.error(f"Failed to initialize broker adapter: {e}")
            return False
            
    def _check_xttrader_available(self) -> bool:
        """检查xttrader是否可用"""
        try:
            import xttrader
            return True
        except ImportError:
            logger.error("xttrader is not installed. Please install miniqmt/xtquant.")
            return False
            
    async def _connect(self) -> bool:
        """连接到交易服务器"""
        try:
            import xttrader
            
            # 创建xttrader实例
            self._xt_trader = xttrader
            
            # 连接到服务器
            connect_result = self._xt_trader.connect()
            if connect_result != 0:
                logger.error(f"Failed to connect to trading server: {connect_result}")
                return False
            
            # 注册回调函数
            self._xt_trader.register_callback(self._on_order_callback)
            self._xt_trader.register_callback(self._on_trade_callback)
            
            self._connected = True
            logger.info("Connected to trading server")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to trading server: {e}")
            self._stats['connection_errors'] += 1
            return False
            
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self._xt_trader and self._connected:
                self._xt_trader.disconnect()
                self._connected = False
                
            logger.info("Disconnected from trading server")
            
        except Exception as e:
            logger.error(f"Error during disconnect: {e}")
            
    async def place_order(self, 
                         symbol: str, 
                         side: OrderSide, 
                         order_type: OrderType,
                         quantity: int, 
                         price: float = 0.0) -> Optional[str]:
        """
        下单
        
        Args:
            symbol: 股票代码
            side: 买卖方向
            order_type: 订单类型
            quantity: 数量
            price: 价格（市价单可为0）
            
        Returns:
            Optional[str]: 订单ID
        """
        try:
            if not self._connected:
                logger.error("Not connected to trading server")
                return None
            
            # 生成订单ID
            order_id = str(uuid.uuid4())
            
            # 创建订单对象
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price
            )
            
            # 转换为xttrader格式
            xt_order_type = self._convert_order_type(order_type)
            xt_side = 1 if side == OrderSide.BUY else -1
            
            # 提交订单
            result = self._xt_trader.order_stock(
                account=self._account_id,
                stock_code=symbol,
                order_type=xt_order_type,
                order_volume=quantity * xt_side,
                price_type=1 if order_type == OrderType.LIMIT else 0,
                price=price if order_type == OrderType.LIMIT else 0
            )
            
            if result > 0:
                # 订单提交成功
                order.status = OrderStatus.SUBMITTED
                self._orders[order_id] = order
                self._stats['orders_submitted'] += 1
                
                # 调用回调函数
                for callback in self._order_callbacks:
                    try:
                        callback(order)
                    except Exception as e:
                        logger.error(f"Error in order callback: {e}")
                
                logger.info(f"Order submitted: {order_id} {symbol} {side.value} {quantity}@{price}")
                return order_id
            else:
                # 订单提交失败
                order.status = OrderStatus.REJECTED
                order.error_message = f"Order rejected with code: {result}"
                self._stats['orders_rejected'] += 1
                
                logger.error(f"Order rejected: {order.error_message}")
                return None
                
        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None
            
    async def cancel_order(self, order_id: str) -> bool:
        """
        撤单
        
        Args:
            order_id: 订单ID
            
        Returns:
            bool: 撤单是否成功
        """
        try:
            if not self._connected:
                logger.error("Not connected to trading server")
                return False
            
            order = self._orders.get(order_id)
            if not order:
                logger.error(f"Order not found: {order_id}")
                return False
            
            if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
                logger.warning(f"Cannot cancel order in status {order.status.value}")
                return False
            
            # 撤销订单
            result = self._xt_trader.cancel_order_stock(
                account=self._account_id,
                order_id=int(order_id.replace('-', '')[:8], 16)  # 简化的ID转换
            )
            
            if result == 0:
                order.status = OrderStatus.CANCELLED
                order.update_time = time.strftime("%Y%m%d %H:%M:%S")
                self._stats['orders_cancelled'] += 1
                
                # 调用回调函数
                for callback in self._order_callbacks:
                    try:
                        callback(order)
                    except Exception as e:
                        logger.error(f"Error in order callback: {e}")
                
                logger.info(f"Order cancelled: {order_id}")
                return True
            else:
                logger.error(f"Failed to cancel order {order_id}: {result}")
                return False
                
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
            
    async def query_orders(self, symbol: Optional[str] = None) -> List[Order]:
        """
        查询订单
        
        Args:
            symbol: 股票代码（可选）
            
        Returns:
            List[Order]: 订单列表
        """
        try:
            if symbol:
                return [order for order in self._orders.values() if order.symbol == symbol]
            else:
                return list(self._orders.values())
                
        except Exception as e:
            logger.error(f"Error querying orders: {e}")
            return []
            
    async def query_positions(self) -> List[Position]:
        """
        查询持仓
        
        Returns:
            List[Position]: 持仓列表
        """
        try:
            if not self._connected:
                logger.error("Not connected to trading server")
                return []
            
            # 查询持仓
            positions_data = self._xt_trader.query_stock_positions(self._account_id)
            
            positions = []
            for pos_data in positions_data:
                position = Position(
                    symbol=pos_data.get('stock_code', ''),
                    quantity=pos_data.get('volume', 0),
                    available_quantity=pos_data.get('can_use_volume', 0),
                    avg_cost=pos_data.get('open_price', 0.0),
                    market_value=pos_data.get('market_value', 0.0),
                    unrealized_pnl=pos_data.get('unrealized_pnl', 0.0),
                    realized_pnl=pos_data.get('realized_pnl', 0.0)
                )
                positions.append(position)
            
            return positions
            
        except Exception as e:
            logger.error(f"Error querying positions: {e}")
            return []
            
    async def query_account(self) -> Optional[Account]:
        """
        查询账户
        
        Returns:
            Optional[Account]: 账户信息
        """
        try:
            if not self._connected:
                logger.error("Not connected to trading server")
                return None
            
            # 查询账户信息
            account_data = self._xt_trader.query_stock_asset(self._account_id)
            
            if account_data:
                account = Account(
                    account_id=self._account_id,
                    total_asset=account_data.get('total_asset', 0.0),
                    available_cash=account_data.get('cash', 0.0),
                    frozen_cash=account_data.get('frozen_cash', 0.0),
                    market_value=account_data.get('market_value', 0.0),
                    total_pnl=account_data.get('total_pnl', 0.0)
                )
                return account
            else:
                return None
                
        except Exception as e:
            logger.error(f"Error querying account: {e}")
            return None
            
    def _convert_order_type(self, order_type: OrderType) -> int:
        """转换订单类型"""
        type_mapping = {
            OrderType.MARKET: 0,
            OrderType.LIMIT: 1,
            OrderType.STOP: 2,
            OrderType.STOP_LIMIT: 3
        }
        return type_mapping.get(order_type, 1)
        
    def _on_order_callback(self, order_data: Dict[str, Any]) -> None:
        """订单回调处理"""
        try:
            order_id = str(order_data.get('order_id', ''))
            order = self._orders.get(order_id)
            
            if order:
                # 更新订单状态
                status_mapping = {
                    0: OrderStatus.PENDING,
                    1: OrderStatus.SUBMITTED,
                    2: OrderStatus.PARTIAL_FILLED,
                    3: OrderStatus.FILLED,
                    4: OrderStatus.CANCELLED,
                    5: OrderStatus.REJECTED
                }
                
                new_status = status_mapping.get(order_data.get('order_status', 0), OrderStatus.PENDING)
                order.status = new_status
                order.filled_quantity = order_data.get('filled_volume', 0)
                order.avg_price = order_data.get('avg_price', 0.0)
                order.update_time = time.strftime("%Y%m%d %H:%M:%S")
                
                if new_status == OrderStatus.FILLED:
                    self._stats['orders_filled'] += 1
                
                # 调用回调函数
                for callback in self._order_callbacks:
                    try:
                        callback(order)
                    except Exception as e:
                        logger.error(f"Error in order callback: {e}")
                        
        except Exception as e:
            logger.error(f"Error processing order callback: {e}")
            
    def _on_trade_callback(self, trade_data: Dict[str, Any]) -> None:
        """成交回调处理"""
        try:
            trade_id = str(trade_data.get('trade_id', ''))
            order_id = str(trade_data.get('order_id', ''))
            
            trade = Trade(
                trade_id=trade_id,
                order_id=order_id,
                symbol=trade_data.get('stock_code', ''),
                side=OrderSide.BUY if trade_data.get('order_volume', 0) > 0 else OrderSide.SELL,
                quantity=abs(trade_data.get('traded_volume', 0)),
                price=trade_data.get('traded_price', 0.0),
                commission=trade_data.get('commission', 0.0)
            )
            
            self._trades[trade_id] = trade
            self._stats['total_trades'] += 1
            
            # 调用回调函数
            for callback in self._trade_callbacks:
                try:
                    callback(trade)
                except Exception as e:
                    logger.error(f"Error in trade callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error processing trade callback: {e}")
            
    def register_order_callback(self, callback: Callable[[Order], None]) -> None:
        """注册订单回调"""
        self._order_callbacks.append(callback)
        
    def register_trade_callback(self, callback: Callable[[Trade], None]) -> None:
        """注册成交回调"""
        self._trade_callbacks.append(callback)
        
    def register_position_callback(self, callback: Callable[[Position], None]) -> None:
        """注册持仓回调"""
        self._position_callbacks.append(callback)
        
    def register_account_callback(self, callback: Callable[[Account], None]) -> None:
        """注册账户回调"""
        self._account_callbacks.append(callback)
        
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self._connected
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self._stats.copy()
